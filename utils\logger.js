const winston = require('winston');
const path = require('path');
const fs = require('fs');

// Ensure logs directory exists
const logsDir = path.join(__dirname, '..', 'logs');
if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
}

// Custom format for console output
const consoleFormat = winston.format.combine(
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    winston.format.colorize(),
    winston.format.printf(({ timestamp, level, message, ...meta }) => {
        let metaStr = '';
        if (Object.keys(meta).length > 0) {
            metaStr = ' ' + JSON.stringify(meta);
        }
        return `${timestamp} [${level}]: ${message}${metaStr}`;
    })
);

// Custom format for file output
const fileFormat = winston.format.combine(
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    winston.format.errors({ stack: true }),
    winston.format.json()
);

// Create logger instance
const logger = winston.createLogger({
    level: process.env.LOG_LEVEL || 'info',
    format: fileFormat,
    defaultMeta: { service: 'bot-platform' },
    transports: [
        // File transport for errors
        new winston.transports.File({
            filename: path.join(logsDir, 'error.log'),
            level: 'error',
            maxsize: 5242880, // 5MB
            maxFiles: 5,
        }),
        
        // File transport for all logs
        new winston.transports.File({
            filename: path.join(logsDir, 'combined.log'),
            maxsize: 5242880, // 5MB
            maxFiles: 5,
        }),
    ],
});

// Add console transport for non-production environments
if (process.env.NODE_ENV !== 'production') {
    logger.add(new winston.transports.Console({
        format: consoleFormat
    }));
}

// Add custom methods for specific use cases
logger.logBotExecution = (botId, executionId, status, details = {}) => {
    logger.info('Bot execution', {
        botId,
        executionId,
        status,
        ...details,
        category: 'bot-execution'
    });
};

logger.logApiRequest = (method, path, userId, statusCode, duration) => {
    logger.info('API request', {
        method,
        path,
        userId,
        statusCode,
        duration,
        category: 'api-request'
    });
};

logger.logSecurityEvent = (event, details = {}) => {
    logger.warn('Security event', {
        event,
        ...details,
        category: 'security'
    });
};

logger.logBotError = (botId, error, context = {}) => {
    logger.error('Bot error', {
        botId,
        error: error.message,
        stack: error.stack,
        ...context,
        category: 'bot-error'
    });
};

module.exports = logger;
