{"name": "bot-creation-platform", "version": "1.0.0", "description": "A comprehensive bot creation platform with sandboxed execution", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "migrate": "node database/migrate.js"}, "keywords": ["bot", "platform", "sandbox", "api", "websocket"], "author": "Bot Platform Team", "license": "MIT", "dependencies": {"axios": "^1.9.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dockerode": "^4.0.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "redis": "^4.6.10", "socket.io": "^4.7.4", "socket.io-client": "^4.8.1", "sqlite3": "^5.1.6", "uuid": "^9.0.1", "winston": "^3.11.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}}