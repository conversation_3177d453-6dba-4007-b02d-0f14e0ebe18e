const crypto = require('crypto');
const { v4: uuidv4 } = require('uuid');

class TokenGenerator {
    /**
     * Generate a secure random token
     * @param {number} length - Token length in bytes (default: 32)
     * @param {string} prefix - Optional prefix for the token
     * @returns {string} Generated token
     */
    static generateSecureToken(length = 32, prefix = '') {
        const token = crypto.randomBytes(length).toString('hex');
        return prefix ? `${prefix}_${token}` : token;
    }

    /**
     * Generate a bot API token
     * @returns {string} Bot API token
     */
    static generateBotToken() {
        return this.generateSecureToken(32, 'bot');
    }

    /**
     * Generate an API key
     * @returns {string} API key
     */
    static generateApiKey() {
        return this.generateSecureToken(32, 'api');
    }

    /**
     * Generate a session token
     * @returns {string} Session token
     */
    static generateSessionToken() {
        return this.generateSecureToken(24, 'sess');
    }

    /**
     * Generate a UUID-based token
     * @param {string} prefix - Optional prefix
     * @returns {string} UUID token
     */
    static generateUuidToken(prefix = '') {
        const uuid = uuidv4().replace(/-/g, '');
        return prefix ? `${prefix}_${uuid}` : uuid;
    }

    /**
     * Generate a short token for temporary use
     * @param {number} length - Token length (default: 8)
     * @returns {string} Short token
     */
    static generateShortToken(length = 8) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let token = '';
        for (let i = 0; i < length; i++) {
            token += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return token;
    }

    /**
     * Generate a numeric token
     * @param {number} length - Token length (default: 6)
     * @returns {string} Numeric token
     */
    static generateNumericToken(length = 6) {
        let token = '';
        for (let i = 0; i < length; i++) {
            token += Math.floor(Math.random() * 10).toString();
        }
        return token;
    }

    /**
     * Validate token format
     * @param {string} token - Token to validate
     * @param {string} expectedPrefix - Expected prefix (optional)
     * @returns {boolean} True if valid
     */
    static validateTokenFormat(token, expectedPrefix = null) {
        if (!token || typeof token !== 'string') {
            return false;
        }

        if (expectedPrefix) {
            if (!token.startsWith(`${expectedPrefix}_`)) {
                return false;
            }
            // Remove prefix for further validation
            token = token.substring(expectedPrefix.length + 1);
        }

        // Check if token contains only valid characters (hex)
        return /^[a-f0-9]+$/i.test(token);
    }

    /**
     * Extract prefix from token
     * @param {string} token - Token with prefix
     * @returns {string|null} Prefix or null if no prefix
     */
    static extractPrefix(token) {
        if (!token || typeof token !== 'string') {
            return null;
        }

        const underscoreIndex = token.indexOf('_');
        if (underscoreIndex === -1) {
            return null;
        }

        return token.substring(0, underscoreIndex);
    }

    /**
     * Generate a token with expiration info
     * @param {number} expiresInMs - Expiration time in milliseconds
     * @param {string} prefix - Optional prefix
     * @returns {object} Token object with token and expiration
     */
    static generateExpiringToken(expiresInMs = 3600000, prefix = '') {
        const token = this.generateSecureToken(32, prefix);
        const expiresAt = new Date(Date.now() + expiresInMs);
        
        return {
            token,
            expiresAt,
            expiresIn: expiresInMs
        };
    }

    /**
     * Generate a token hash for storage
     * @param {string} token - Token to hash
     * @returns {string} Hashed token
     */
    static hashToken(token) {
        return crypto.createHash('sha256').update(token).digest('hex');
    }

    /**
     * Verify token against hash
     * @param {string} token - Original token
     * @param {string} hash - Stored hash
     * @returns {boolean} True if token matches hash
     */
    static verifyTokenHash(token, hash) {
        const tokenHash = this.hashToken(token);
        return crypto.timingSafeEqual(
            Buffer.from(tokenHash, 'hex'),
            Buffer.from(hash, 'hex')
        );
    }

    /**
     * Generate a rate limiting key
     * @param {string} identifier - User/IP identifier
     * @param {string} action - Action being rate limited
     * @returns {string} Rate limiting key
     */
    static generateRateLimitKey(identifier, action) {
        return `rate_limit:${action}:${identifier}`;
    }

    /**
     * Generate a cache key
     * @param {string} namespace - Cache namespace
     * @param {string} identifier - Cache identifier
     * @returns {string} Cache key
     */
    static generateCacheKey(namespace, identifier) {
        return `cache:${namespace}:${identifier}`;
    }

    /**
     * Generate a webhook secret
     * @returns {string} Webhook secret
     */
    static generateWebhookSecret() {
        return this.generateSecureToken(64, 'whsec');
    }

    /**
     * Generate a one-time password (OTP)
     * @param {number} length - OTP length (default: 6)
     * @returns {string} OTP
     */
    static generateOTP(length = 6) {
        return this.generateNumericToken(length);
    }

    /**
     * Generate a recovery code
     * @returns {string} Recovery code
     */
    static generateRecoveryCode() {
        const segments = [];
        for (let i = 0; i < 4; i++) {
            segments.push(this.generateShortToken(4));
        }
        return segments.join('-');
    }
}

module.exports = TokenGenerator;
