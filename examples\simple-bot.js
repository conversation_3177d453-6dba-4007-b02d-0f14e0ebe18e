#!/usr/bin/env node

const { BotClient } = require('../bot-sdk/BotClient');

// Bot token - get this from the web interface
const BOT_TOKEN = process.env.BOT_TOKEN || 'bot_17499c279d36430590f36b56d6b3b691';

/**
 * Simple Echo Bot Example
 * This bot responds to messages with various commands
 */
class SimpleBot {
    constructor(token) {
        this.client = new BotClient(token);
        this.setupEventHandlers();
    }

    setupEventHandlers() {
        // Bot ready event
        this.client.on('ready', (bot) => {
            console.log(`🚀 ${bot.name} is ready!`);
            console.log(`📋 Bot ID: ${bot.id}`);
            
            // Send startup message
            this.client.sendMessage('🤖 Bot is online and ready to help!');
        });

        // Message received event
        this.client.on('message', async (message) => {
            console.log(`📨 Received: "${message.content}" (${message.type})`);
            
            // Only respond to user messages
            if (message.isFromUser()) {
                await this.handleUserMessage(message);
            }
        });

        // Error handling
        this.client.on('error', (error) => {
            console.error('🚫 Bot error:', error);
        });

        // Disconnect handling
        this.client.on('disconnect', () => {
            console.log('🔌 Bot disconnected');
        });
    }

    async handleUserMessage(message) {
        const content = message.content.toLowerCase().trim();
        
        try {
            // Command handling
            if (content.startsWith('!')) {
                await this.handleCommand(message, content);
            } else {
                // Regular conversation
                await this.handleConversation(message, content);
            }
        } catch (error) {
            console.error('Error handling message:', error);
            await message.reply('❌ Sorry, I encountered an error processing your message.');
        }
    }

    async handleCommand(message, content) {
        const [command, ...args] = content.slice(1).split(' ');
        
        switch (command) {
            case 'help':
                await this.showHelp(message);
                break;
                
            case 'ping':
                await message.reply('🏓 Pong!');
                break;
                
            case 'time':
                await message.reply(`🕐 Current time: ${new Date().toLocaleString()}`);
                break;
                
            case 'echo':
                const text = args.join(' ');
                await message.reply(`🔊 Echo: ${text || 'Nothing to echo!'}`);
                break;
                
            case 'calc':
                await this.handleCalculation(message, args.join(' '));
                break;
                
            case 'joke':
                await this.tellJoke(message);
                break;
                
            case 'status':
                await this.showStatus(message);
                break;
                
            case 'code':
                await this.executeCode(message, args.join(' '));
                break;
                
            default:
                await message.reply(`❓ Unknown command: ${command}. Type !help for available commands.`);
        }
    }

    async handleConversation(message, content) {
        // Simple conversation responses
        const responses = {
            'hello': '👋 Hello there! How can I help you today?',
            'hi': '👋 Hi! Nice to meet you!',
            'how are you': '🤖 I\'m doing great! Thanks for asking. How are you?',
            'what is your name': '🤖 I\'m a simple bot created with the Bot Platform!',
            'thank you': '😊 You\'re welcome! Happy to help!',
            'thanks': '😊 No problem!',
            'bye': '👋 Goodbye! Have a great day!',
            'goodbye': '👋 See you later!'
        };

        // Check for exact matches
        if (responses[content]) {
            await message.reply(responses[content]);
            return;
        }

        // Check for partial matches
        for (const [key, response] of Object.entries(responses)) {
            if (content.includes(key)) {
                await message.reply(response);
                return;
            }
        }

        // Default response
        const defaultResponses = [
            '🤔 That\'s interesting! Tell me more.',
            '💭 I see what you mean.',
            '🤖 I\'m still learning. Could you rephrase that?',
            '📚 That\'s something I\'d like to learn more about!',
            '💡 Interesting perspective! Type !help to see what I can do.'
        ];
        
        const randomResponse = defaultResponses[Math.floor(Math.random() * defaultResponses.length)];
        await message.reply(randomResponse);
    }

    async showHelp(message) {
        const helpText = `
🤖 **Bot Commands Help**

**Basic Commands:**
• \`!help\` - Show this help message
• \`!ping\` - Test bot responsiveness
• \`!time\` - Get current time
• \`!echo <text>\` - Echo your message
• \`!status\` - Show bot status

**Fun Commands:**
• \`!joke\` - Get a random joke
• \`!calc <expression>\` - Simple calculator

**Advanced:**
• \`!code <javascript>\` - Execute JavaScript code

**Conversation:**
Just type normally and I'll try to respond! I understand basic greetings and questions.

Type any command to get started! 🚀
        `;
        
        await message.reply(helpText);
    }

    async handleCalculation(message, expression) {
        try {
            // Simple safe calculation (only allow basic math)
            const safeExpression = expression.replace(/[^0-9+\-*/().\s]/g, '');
            
            if (!safeExpression) {
                await message.reply('❌ Please provide a valid math expression. Example: !calc 2 + 2');
                return;
            }
            
            const result = eval(safeExpression);
            await message.reply(`🧮 ${expression} = ${result}`);
            
        } catch (error) {
            await message.reply('❌ Invalid math expression. Please use basic operators (+, -, *, /, parentheses).');
        }
    }

    async tellJoke(message) {
        const jokes = [
            'Why don\'t scientists trust atoms? Because they make up everything! 😄',
            'Why did the robot go on a diet? It had a byte problem! 🤖',
            'What do you call a bot that takes the long way around? R2-Detour! 🛸',
            'Why don\'t bots ever get tired? They always get their byte of sleep! 💤',
            'What\'s a robot\'s favorite type of music? Heavy metal! 🎵'
        ];
        
        const randomJoke = jokes[Math.floor(Math.random() * jokes.length)];
        await message.reply(randomJoke);
    }

    async showStatus(message) {
        const bot = this.client.getBot();
        const uptime = process.uptime();
        const memoryUsage = process.memoryUsage();
        
        const statusText = `
🤖 **Bot Status**

• **Name:** ${bot.name}
• **ID:** ${bot.id}
• **Status:** ${this.client.isConnected() ? '🟢 Online' : '🔴 Offline'}
• **Uptime:** ${Math.floor(uptime / 60)} minutes
• **Memory:** ${Math.round(memoryUsage.heapUsed / 1024 / 1024)} MB
• **Platform:** Node.js ${process.version}

All systems operational! ✅
        `;
        
        await message.reply(statusText);
    }

    async executeCode(message, code) {
        if (!code) {
            await message.reply('❌ Please provide JavaScript code to execute. Example: !code console.log("Hello World")');
            return;
        }
        
        try {
            const result = await this.client.executeCode(code);
            
            if (result.status === 'completed') {
                await message.reply(`✅ Code executed successfully!\n\`\`\`\n${result.output}\n\`\`\``);
            } else {
                await message.reply(`❌ Code execution failed:\n\`\`\`\n${result.error}\n\`\`\``);
            }
            
        } catch (error) {
            await message.reply(`❌ Failed to execute code: ${error.message}`);
        }
    }

    async start() {
        try {
            await this.client.login();
        } catch (error) {
            console.error('❌ Failed to start bot:', error.message);
            process.exit(1);
        }
    }

    stop() {
        this.client.disconnect();
        process.exit(0);
    }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n👋 Shutting down bot...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n👋 Shutting down bot...');
    process.exit(0);
});

// Start bot if this file is run directly
if (require.main === module) {
    if (!BOT_TOKEN || BOT_TOKEN === 'your-bot-token-here') {
        console.error('❌ Please set BOT_TOKEN environment variable or update the token in the code');
        console.log('💡 Get your bot token from the web interface at http://localhost:3000');
        process.exit(1);
    }
    
    console.log('🚀 Starting Simple Bot...');
    console.log(`🔑 Using token: ${BOT_TOKEN.substring(0, 20)}...`);
    
    const bot = new SimpleBot(BOT_TOKEN);
    bot.start();
}

module.exports = SimpleBot;
