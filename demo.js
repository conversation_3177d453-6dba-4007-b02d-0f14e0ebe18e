#!/usr/bin/env node

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

class BotPlatformDemo {
    constructor() {
        this.authToken = null;
        this.botId = null;
        this.botToken = null;
    }

    async makeRequest(method, endpoint, data = null, headers = {}) {
        try {
            const config = {
                method,
                url: `${BASE_URL}${endpoint}`,
                headers: {
                    'Content-Type': 'application/json',
                    ...headers
                }
            };

            if (data) {
                config.data = data;
            }

            const response = await axios(config);
            return response.data;
        } catch (error) {
            console.error(`Error ${method} ${endpoint}:`, error.response?.data || error.message);
            throw error;
        }
    }

    async step(title, fn) {
        console.log(`\n🔄 ${title}...`);
        try {
            const result = await fn();
            console.log(`✅ ${title} - Success`);
            if (result) {
                console.log('   Result:', JSON.stringify(result, null, 2));
            }
            return result;
        } catch (error) {
            console.log(`❌ ${title} - Failed`);
            console.error('   Error:', error.message);
            throw error;
        }
    }

    async demo() {
        console.log('🚀 Bot Creation Platform Demo');
        console.log('================================');

        try {
            // 1. Health Check
            await this.step('Health Check', async () => {
                return await this.makeRequest('GET', '/health');
            });

            // 2. Register User
            const userData = {
                username: 'demouser',
                email: '<EMAIL>',
                password: 'demopassword123'
            };

            const registerResult = await this.step('Register User', async () => {
                return await this.makeRequest('POST', '/api/auth/register', userData);
            });

            this.authToken = registerResult.token;

            // 3. Login User
            await this.step('Login User', async () => {
                return await this.makeRequest('POST', '/api/auth/login', {
                    email: userData.email,
                    password: userData.password
                });
            });

            // 4. Get User Profile
            await this.step('Get User Profile', async () => {
                return await this.makeRequest('GET', '/api/auth/profile', null, {
                    'Authorization': `Bearer ${this.authToken}`
                });
            });

            console.log('\n🎉 Demo completed successfully!');
            console.log('\n📋 Summary:');
            console.log(`   - User registered and authenticated`);
            console.log(`   - Auth token: ${this.authToken.substring(0, 20)}...`);
            console.log('\n🔧 Next Steps:');
            console.log('   - Add bot management routes to simple-server.js');
            console.log('   - Test bot creation and token generation');
            console.log('   - Implement Docker sandbox for code execution');

        } catch (error) {
            console.log('\n💥 Demo failed:', error.message);
            process.exit(1);
        }
    }
}

// Run demo if this file is executed directly
if (require.main === module) {
    const demo = new BotPlatformDemo();
    demo.demo().then(() => {
        console.log('\n✨ Demo finished');
        process.exit(0);
    }).catch(error => {
        console.error('\n💥 Demo error:', error.message);
        process.exit(1);
    });
}

module.exports = BotPlatformDemo;
