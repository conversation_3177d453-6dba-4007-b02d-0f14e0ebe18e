require('dotenv').config();
const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const helmet = require('helmet');
const path = require('path');
const { initializeDatabase } = require('./config/database');
const authRoutes = require('./routes/auth');
const botRoutes = require('./routes/bots');
const logger = require('./utils/logger');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
    cors: {
        origin: "*",
        methods: ["GET", "POST"]
    }
});
const port = process.env.PORT || 3000;

// Middleware
app.use(helmet());
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Logging middleware
app.use((req, res, next) => {
    logger.info(`${req.method} ${req.path} - ${req.ip}`);
    next();
});

// Health check
app.get('/health', (req, res) => {
    res.json({ 
        status: 'healthy', 
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});

// Socket.IO setup
io.on('connection', (socket) => {
    logger.info('Socket connection established', { socketId: socket.id });

    socket.on('authenticate', (data) => {
        // Simple authentication for demo
        socket.emit('authenticated', { success: true });
    });

    socket.on('join_bot_room', (data) => {
        const roomName = `bot:${data.botId}`;
        socket.join(roomName);
        socket.emit('joined_bot_room', data);
    });

    socket.on('send_message_to_bot', (data) => {
        // Echo message back as bot response for demo
        setTimeout(() => {
            socket.emit('bot_message', {
                type: 'bot',
                content: `Bot received: "${data.content}". This is a demo response!`,
                timestamp: new Date().toISOString()
            });
        }, 1000);
    });

    socket.on('disconnect', () => {
        logger.info('Socket disconnected', { socketId: socket.id });
    });
});

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/bots', botRoutes);

// Bot API routes (for bot code to connect)
const apiRoutes = require('./routes/api');
app.use('/api', apiRoutes);

// Serve index.html for all non-API routes
app.get('*', (req, res) => {
    if (!req.path.startsWith('/api')) {
        res.sendFile(path.join(__dirname, 'public', 'index.html'));
    } else {
        res.status(404).json({ error: 'API route not found' });
    }
});

// Error handler
app.use((error, req, res, next) => {
    logger.error('Unhandled error:', error);
    
    if (res.headersSent) {
        return next(error);
    }

    const statusCode = error.statusCode || 500;
    const message = process.env.NODE_ENV === 'production' 
        ? 'Internal server error' 
        : error.message;

    res.status(statusCode).json({
        error: message,
        ...(process.env.NODE_ENV !== 'production' && { stack: error.stack })
    });
});

// Initialize and start server
async function startServer() {
    try {
        // Initialize database
        await initializeDatabase();
        logger.info('Database initialized successfully');

        server.listen(port, () => {
            logger.info(`Simple Bot Platform Server running on port ${port}`);
            logger.info(`Environment: ${process.env.NODE_ENV}`);
            logger.info(`WebSocket server enabled`);
        });
    } catch (error) {
        logger.error('Failed to start server:', error);
        process.exit(1);
    }
}

startServer();
