# 🤖 Bot Development Guide

Hướng dẫn tạo bot giống Discord trên Bot Platform

## 🎯 Kiến trúc Bot Platform (giống Discord)

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Interface │    │  Platform API   │    │   Your Bot Code │
│                 │    │                 │    │                 │
│ • Create Bot    │◄──►│ • Authentication│◄──►│ • Bot Logic     │
│ • Get Token     │    │ • Message API   │    │ • Event Handlers│
│ • Manage Bots   │    │ • WebSocket     │    │ • Commands      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 Bước 1: Tạo Bot và lấy Token

### 1.1. Truy cập Web Interface
```bash
# Khởi động server
node simple-server.js

# Mở trình duyệt
http://localhost:3000
```

### 1.2. Tạo Bot
1. **Đăng nhập** với tài khoản của bạn
2. **Click dấu +** bên cạnh "BOTS" 
3. **Điền thông tin bot:**
   - Name: `My Awesome Bot`
   - Description: `A cool bot that does amazing things`
   - Language: `JavaScript`
4. **Click "Create Bot"**

### 1.3. <PERSON><PERSON><PERSON>
1. **Chọn bot** vừa tạo từ sidebar
2. **Click "Execute Code"** (hoặc dùng API)
3. **Hoặc dùng script:**
```bash
node get-bot-token.js <EMAIL> your-password
```

## 🛠️ Bước 2: Viết Bot Code

### 2.1. Simple Echo Bot
```javascript
const { BotClient } = require('./bot-sdk/BotClient');

const bot = new BotClient('your-bot-token-here');

// Bot ready event
bot.on('ready', (botInfo) => {
    console.log(`Bot ${botInfo.name} is online!`);
    bot.sendMessage('🤖 Hello! I am now online!');
});

// Message received event
bot.on('message', async (message) => {
    if (message.isFromUser()) {
        await message.reply(`You said: ${message.content}`);
    }
});

// Start bot
bot.login();
```

### 2.2. Advanced Command Bot
```javascript
const { BotClient } = require('./bot-sdk/BotClient');

class MyBot {
    constructor(token) {
        this.client = new BotClient(token);
        this.setupEvents();
    }

    setupEvents() {
        this.client.on('ready', () => {
            console.log('Bot ready!');
        });

        this.client.on('message', async (message) => {
            if (message.isFromUser()) {
                await this.handleMessage(message);
            }
        });
    }

    async handleMessage(message) {
        const content = message.content.toLowerCase();

        if (content.startsWith('!')) {
            await this.handleCommand(message);
        } else {
            await this.handleChat(message);
        }
    }

    async handleCommand(message) {
        const [command, ...args] = message.content.slice(1).split(' ');

        switch (command) {
            case 'ping':
                await message.reply('🏓 Pong!');
                break;
            
            case 'time':
                await message.reply(`🕐 ${new Date().toLocaleString()}`);
                break;
            
            case 'help':
                await message.reply(`
🤖 **Available Commands:**
• !ping - Test bot
• !time - Get current time
• !help - Show this help
                `);
                break;
            
            default:
                await message.reply(`❓ Unknown command: ${command}`);
        }
    }

    async handleChat(message) {
        // Simple AI-like responses
        const responses = [
            "That's interesting! 🤔",
            "Tell me more about that! 💭",
            "I see what you mean! 👍",
            "Thanks for sharing! 😊"
        ];
        
        const randomResponse = responses[Math.floor(Math.random() * responses.length)];
        await message.reply(randomResponse);
    }

    async start() {
        await this.client.login();
    }
}

// Usage
const bot = new MyBot('your-bot-token-here');
bot.start();
```

## 🎮 Bước 3: Chạy Bot

### 3.1. Quick Test
```bash
# Sử dụng bot runner
node run-bot.js your-bot-token-here

# Hoặc với environment variable
BOT_TOKEN=your-bot-token node run-bot.js
```

### 3.2. Chạy Example Bot
```bash
# Chạy simple bot với nhiều tính năng
BOT_TOKEN=your-bot-token node examples/simple-bot.js
```

### 3.3. Chạy Custom Bot
```bash
# Tạo file my-bot.js với code của bạn
node my-bot.js
```

## 📚 Bot SDK API Reference

### BotClient Class

#### Constructor
```javascript
const bot = new BotClient(token, options);
```

#### Methods
- `await bot.login()` - Kết nối bot
- `await bot.sendMessage(content, metadata)` - Gửi tin nhắn
- `await bot.executeCode(code, input, timeout)` - Chạy code
- `await bot.getState()` - Lấy bot state
- `await bot.setState(state, ttl)` - Cập nhật state
- `bot.on(event, handler)` - Đăng ký event handler
- `bot.disconnect()` - Ngắt kết nối

#### Events
- `ready` - Bot đã sẵn sàng
- `message` - Nhận tin nhắn mới
- `error` - Lỗi xảy ra
- `disconnect` - Mất kết nối

### Message Class

#### Properties
- `message.id` - ID tin nhắn
- `message.content` - Nội dung
- `message.type` - Loại tin nhắn
- `message.metadata` - Metadata
- `message.timestamp` - Thời gian

#### Methods
- `await message.reply(content, metadata)` - Trả lời tin nhắn
- `message.isFromUser()` - Kiểm tra từ user
- `message.getAge()` - Tuổi tin nhắn (ms)

## 🎯 Use Cases

### 1. Echo Bot
```javascript
bot.on('message', async (message) => {
    if (message.isFromUser()) {
        await message.reply(`Echo: ${message.content}`);
    }
});
```

### 2. Command Bot
```javascript
bot.on('message', async (message) => {
    if (message.content.startsWith('!ping')) {
        await message.reply('Pong! 🏓');
    }
});
```

### 3. AI Chatbot
```javascript
bot.on('message', async (message) => {
    if (message.isFromUser()) {
        // Call your AI API here
        const response = await callAI(message.content);
        await message.reply(response);
    }
});
```

### 4. Utility Bot
```javascript
bot.on('message', async (message) => {
    const content = message.content.toLowerCase();
    
    if (content.includes('weather')) {
        const weather = await getWeather();
        await message.reply(`🌤️ Weather: ${weather}`);
    }
    
    if (content.includes('time')) {
        await message.reply(`🕐 Time: ${new Date().toLocaleString()}`);
    }
});
```

### 5. Stateful Bot
```javascript
bot.on('message', async (message) => {
    // Get user state
    const state = await bot.getState();
    const userState = state[message.userId] || { step: 0 };
    
    // Handle conversation flow
    if (userState.step === 0) {
        await message.reply("What's your name?");
        userState.step = 1;
    } else if (userState.step === 1) {
        userState.name = message.content;
        userState.step = 2;
        await message.reply(`Nice to meet you, ${userState.name}!`);
    }
    
    // Save state
    state[message.userId] = userState;
    await bot.setState(state);
});
```

## 🔧 Development Tips

### 1. Error Handling
```javascript
bot.on('message', async (message) => {
    try {
        // Your bot logic here
        await message.reply('Success!');
    } catch (error) {
        console.error('Error:', error);
        await message.reply('❌ Something went wrong!');
    }
});
```

### 2. Rate Limiting
```javascript
const lastMessage = new Map();

bot.on('message', async (message) => {
    const userId = message.metadata.userId;
    const now = Date.now();
    
    if (lastMessage.has(userId)) {
        const timeDiff = now - lastMessage.get(userId);
        if (timeDiff < 1000) { // 1 second cooldown
            return; // Ignore rapid messages
        }
    }
    
    lastMessage.set(userId, now);
    await message.reply('Hello!');
});
```

### 3. Logging
```javascript
bot.on('message', async (message) => {
    console.log(`[${new Date().toISOString()}] ${message.type}: ${message.content}`);
    // Handle message...
});
```

## 🚀 Deployment

### 1. Local Development
```bash
node my-bot.js
```

### 2. PM2 (Production)
```bash
npm install -g pm2
pm2 start my-bot.js --name "my-awesome-bot"
pm2 logs my-awesome-bot
```

### 3. Docker
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
CMD ["node", "my-bot.js"]
```

## 🎉 Kết luận

Bây giờ bạn có thể tạo bot giống như Discord:

1. **Tạo bot** qua web interface
2. **Lấy token** để authenticate
3. **Viết code** sử dụng Bot SDK
4. **Deploy** và chạy bot của bạn

Bot sẽ xuất hiện như một user trong chat interface và có thể tương tác real-time với users! 🤖✨
