const Docker = require('dockerode');
const fs = require('fs').promises;
const path = require('path');
const logger = require('../utils/logger');

class DockerSandbox {
    constructor() {
        this.docker = new Docker({
            socketPath: process.env.DOCKER_SOCKET || '/var/run/docker.sock'
        });
        this.activeContainers = new Map();
        this.sandboxImage = process.env.BOT_SANDBOX_IMAGE || 'bot-sandbox:latest';
        this.maxMemory = process.env.MAX_BOT_MEMORY || '128m';
        this.maxCpu = parseFloat(process.env.MAX_BOT_CPU) || 0.5;
        this.networkIsolation = process.env.BOT_NETWORK_ISOLATION === 'true';
    }

    async initialize() {
        try {
            // Check if Docker is available
            await this.docker.ping();
            logger.info('Docker connection established');

            // Build sandbox image if it doesn't exist
            await this.ensureSandboxImage();
            
            logger.info('Docker sandbox initialized successfully');
        } catch (error) {
            logger.error('Docker sandbox initialization failed:', error);
            throw error;
        }
    }

    async ensureSandboxImage() {
        try {
            // Check if image exists
            const images = await this.docker.listImages({
                filters: { reference: [this.sandboxImage] }
            });

            if (images.length === 0) {
                logger.info('Sandbox image not found, building...');
                await this.buildSandboxImage();
            } else {
                logger.info('Sandbox image found');
            }
        } catch (error) {
            logger.error('Error checking sandbox image:', error);
            throw error;
        }
    }

    async buildSandboxImage() {
        try {
            // Create Dockerfile content
            const dockerfileContent = this.generateDockerfile();
            
            // Write Dockerfile to sandbox directory
            const sandboxDir = path.join(__dirname, '..', 'sandbox');
            await fs.mkdir(sandboxDir, { recursive: true });
            await fs.writeFile(path.join(sandboxDir, 'Dockerfile'), dockerfileContent);

            // Create runner script
            const runnerScript = this.generateRunnerScript();
            await fs.writeFile(path.join(sandboxDir, 'runner.js'), runnerScript);

            // Build image
            const stream = await this.docker.buildImage({
                context: sandboxDir,
                src: ['Dockerfile', 'runner.js']
            }, {
                t: this.sandboxImage
            });

            // Wait for build to complete
            await new Promise((resolve, reject) => {
                this.docker.modem.followProgress(stream, (err, res) => {
                    if (err) reject(err);
                    else resolve(res);
                }, (event) => {
                    if (event.stream) {
                        logger.info('Docker build:', event.stream.trim());
                    }
                });
            });

            logger.info('Sandbox image built successfully');
        } catch (error) {
            logger.error('Error building sandbox image:', error);
            throw error;
        }
    }

    generateDockerfile() {
        return `
FROM node:18-alpine

# Install Python for multi-language support
RUN apk add --no-cache python3 py3-pip

# Create app directory
WORKDIR /app

# Create non-root user
RUN addgroup -g 1001 -S botuser && \\
    adduser -S botuser -u 1001 -G botuser

# Copy runner script
COPY runner.js /app/runner.js

# Set permissions
RUN chown -R botuser:botuser /app
USER botuser

# Set resource limits
ENV NODE_OPTIONS="--max-old-space-size=64"

CMD ["node", "runner.js"]
`;
    }

    generateRunnerScript() {
        return `const fs = require('fs');
const vm = require('vm');
const { spawn } = require('child_process');

class CodeRunner {
    constructor() {
        this.timeout = parseInt(process.env.TIMEOUT) || 10000;
        this.language = process.env.LANGUAGE || 'javascript';
    }

    async run() {
        try {
            const code = process.env.CODE;
            const input = process.env.INPUT ? JSON.parse(process.env.INPUT) : null;

            let result;
            if (this.language === 'javascript') {
                result = await this.runJavaScript(code, input);
            } else if (this.language === 'python') {
                result = await this.runPython(code, input);
            } else {
                throw new Error('Unsupported language: ' + this.language);
            }

            console.log(JSON.stringify({
                status: 'completed',
                output: result,
                error: null
            }));
        } catch (error) {
            console.log(JSON.stringify({
                status: 'failed',
                output: null,
                error: error.message
            }));
        }
    }

    async runJavaScript(code, input) {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Execution timeout'));
            }, this.timeout);

            try {
                // Create sandbox context
                const sandbox = {
                    input,
                    output: undefined,
                    console: {
                        log: (...args) => {
                            sandbox.output = args.join(' ');
                        }
                    },
                    setTimeout: () => { throw new Error('setTimeout is not allowed'); },
                    setInterval: () => { throw new Error('setInterval is not allowed'); },
                    require: () => { throw new Error('require is not allowed'); }
                };

                // Execute code in sandbox
                const context = vm.createContext(sandbox);
                const script = new vm.Script(code);
                script.runInContext(context, { timeout: this.timeout });

                clearTimeout(timeout);
                resolve(sandbox.output);
            } catch (error) {
                clearTimeout(timeout);
                reject(error);
            }
        });
    }

    async runPython(code, input) {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                if (pythonProcess) {
                    pythonProcess.kill('SIGKILL');
                }
                reject(new Error('Execution timeout'));
            }, this.timeout);

            // Create Python script with input
            const pythonScript = \`
import json
import sys

# Input data
input_data = \` + JSON.stringify(JSON.stringify(input)) + \`

# User code
\` + code + \`
\`;

            let pythonProcess;
            let output = '';
            let error = '';

            try {
                pythonProcess = spawn('python3', ['-c', pythonScript], {
                    stdio: ['pipe', 'pipe', 'pipe'],
                    timeout: this.timeout
                });

                pythonProcess.stdout.on('data', (data) => {
                    output += data.toString();
                });

                pythonProcess.stderr.on('data', (data) => {
                    error += data.toString();
                });

                pythonProcess.on('close', (code) => {
                    clearTimeout(timeout);
                    if (code === 0) {
                        resolve(output.trim());
                    } else {
                        reject(new Error(error || 'Python process exited with code ' + code));
                    }
                });

                pythonProcess.on('error', (err) => {
                    clearTimeout(timeout);
                    reject(err);
                });
            } catch (error) {
                clearTimeout(timeout);
                reject(error);
            }
        });
    }
}

// Run the code
const runner = new CodeRunner();
runner.run().catch(error => {
    console.log(JSON.stringify({
        status: 'failed',
        output: null,
        error: error.message
    }));
});
`;
    }

    async execute(options) {
        const {
            executionId,
            botId,
            code,
            input,
            timeout,
            language
        } = options;

        let container = null;

        try {
            // Create container
            container = await this.docker.createContainer({
                Image: this.sandboxImage,
                Env: [
                    `CODE=${code}`,
                    `INPUT=${JSON.stringify(input)}`,
                    `TIMEOUT=${timeout}`,
                    `LANGUAGE=${language}`
                ],
                HostConfig: {
                    Memory: this.parseMemoryLimit(this.maxMemory),
                    CpuQuota: Math.floor(this.maxCpu * 100000),
                    CpuPeriod: 100000,
                    NetworkMode: this.networkIsolation ? 'none' : 'default',
                    ReadonlyRootfs: true,
                    AutoRemove: true,
                    PidsLimit: 50
                },
                WorkingDir: '/app',
                User: 'botuser'
            });

            // Store container reference
            this.activeContainers.set(executionId, container);

            // Start container
            await container.start();

            // Wait for execution with timeout
            const result = await Promise.race([
                this.waitForContainer(container),
                this.createTimeoutPromise(timeout + 5000) // Add 5s buffer
            ]);

            // Remove from active containers
            this.activeContainers.delete(executionId);

            return result;
        } catch (error) {
            // Cleanup
            if (container) {
                try {
                    await container.remove({ force: true });
                } catch (cleanupError) {
                    logger.error('Container cleanup error:', cleanupError);
                }
            }
            this.activeContainers.delete(executionId);

            throw error;
        }
    }

    async waitForContainer(container) {
        try {
            // Get container logs
            const stream = await container.logs({
                stdout: true,
                stderr: true,
                follow: true
            });

            let output = '';
            
            return new Promise((resolve, reject) => {
                stream.on('data', (chunk) => {
                    output += chunk.toString();
                });

                stream.on('end', () => {
                    try {
                        // Parse the JSON output from runner script
                        const result = JSON.parse(output.trim());
                        resolve(result);
                    } catch (parseError) {
                        resolve({
                            status: 'failed',
                            output: null,
                            error: 'Failed to parse execution result'
                        });
                    }
                });

                stream.on('error', (error) => {
                    reject(error);
                });

                // Wait for container to finish
                container.wait().then((result) => {
                    if (result.StatusCode !== 0) {
                        resolve({
                            status: 'failed',
                            output: null,
                            error: `Container exited with code ${result.StatusCode}`
                        });
                    }
                }).catch(reject);
            });
        } catch (error) {
            throw error;
        }
    }

    createTimeoutPromise(timeout) {
        return new Promise((_, reject) => {
            setTimeout(() => {
                reject(new Error('Container execution timeout'));
            }, timeout);
        });
    }

    async cancelExecution(executionId) {
        try {
            const container = this.activeContainers.get(executionId);
            if (container) {
                await container.kill();
                await container.remove({ force: true });
                this.activeContainers.delete(executionId);
                return true;
            }
            return false;
        } catch (error) {
            logger.error('Cancel execution error:', error);
            return false;
        }
    }

    async getResourceUsage() {
        try {
            const containers = await this.docker.listContainers();
            const sandboxContainers = containers.filter(container => 
                container.Image === this.sandboxImage
            );

            return {
                activeContainers: this.activeContainers.size,
                totalSandboxContainers: sandboxContainers.length
            };
        } catch (error) {
            logger.error('Get Docker resource usage error:', error);
            return null;
        }
    }

    parseMemoryLimit(memoryString) {
        const units = {
            'b': 1,
            'k': 1024,
            'm': 1024 * 1024,
            'g': 1024 * 1024 * 1024
        };

        const match = memoryString.toLowerCase().match(/^(\d+)([bkmg]?)$/);
        if (!match) {
            return 128 * 1024 * 1024; // Default 128MB
        }

        const value = parseInt(match[1]);
        const unit = match[2] || 'b';
        return value * units[unit];
    }

    async shutdown() {
        try {
            logger.info('Shutting down Docker sandbox...');

            // Cancel all active executions
            const activeExecutionIds = Array.from(this.activeContainers.keys());
            for (const executionId of activeExecutionIds) {
                await this.cancelExecution(executionId);
            }

            logger.info('Docker sandbox shutdown complete');
        } catch (error) {
            logger.error('Docker sandbox shutdown error:', error);
        }
    }
}

module.exports = DockerSandbox;
