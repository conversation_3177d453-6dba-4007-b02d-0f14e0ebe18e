const jwt = require('jsonwebtoken');
const { getDatabase } = require('../config/database');
const { getRedis } = require('../config/redis');
const logger = require('../utils/logger');

class SocketManager {
    constructor(io, botRuntime) {
        this.io = io;
        this.botRuntime = botRuntime;
        this.connectedUsers = new Map(); // userId -> socket
        this.connectedBots = new Map(); // botId -> socket
        this.userSockets = new Map(); // socketId -> userId
        this.botSockets = new Map(); // socketId -> botId
    }

    initialize() {
        this.io.on('connection', (socket) => {
            logger.info('Socket connection established', { socketId: socket.id });

            // Handle authentication
            socket.on('authenticate', async (data) => {
                await this.handleAuthentication(socket, data);
            });

            // Handle bot authentication
            socket.on('authenticate_bot', async (data) => {
                await this.handleBotAuthentication(socket, data);
            });

            // Handle user events
            socket.on('join_bot_room', async (data) => {
                await this.handleJoinBotRoom(socket, data);
            });

            socket.on('leave_bot_room', async (data) => {
                await this.handleLeaveBotRoom(socket, data);
            });

            socket.on('execute_bot_code', async (data) => {
                await this.handleExecuteBotCode(socket, data);
            });

            socket.on('send_message_to_bot', async (data) => {
                await this.handleSendMessageToBot(socket, data);
            });

            // Handle bot events
            socket.on('bot_message', async (data) => {
                await this.handleBotMessage(socket, data);
            });

            socket.on('bot_status_update', async (data) => {
                await this.handleBotStatusUpdate(socket, data);
            });

            // Handle disconnection
            socket.on('disconnect', () => {
                this.handleDisconnection(socket);
            });

            // Handle errors
            socket.on('error', (error) => {
                logger.error('Socket error:', error, { socketId: socket.id });
            });
        });

        // Subscribe to Redis channels for bot messages
        this.subscribeToRedisChannels();

        logger.info('Socket manager initialized');
    }

    async handleAuthentication(socket, data) {
        try {
            const { token } = data;
            
            if (!token) {
                socket.emit('auth_error', { error: 'Token required' });
                return;
            }

            // Verify JWT token
            const decoded = jwt.verify(token, process.env.JWT_SECRET);
            
            // Get user from database
            const db = getDatabase();
            const user = await db.get(
                'SELECT id, username, email, role, is_active FROM users WHERE id = ?',
                [decoded.userId]
            );

            if (!user || !user.is_active) {
                socket.emit('auth_error', { error: 'Invalid user' });
                return;
            }

            // Store user connection
            socket.userId = user.id;
            socket.userRole = user.role;
            this.connectedUsers.set(user.id, socket);
            this.userSockets.set(socket.id, user.id);

            socket.emit('authenticated', { 
                user: { 
                    id: user.id, 
                    username: user.username, 
                    role: user.role 
                } 
            });

            logger.info('User authenticated via socket', { 
                userId: user.id, 
                username: user.username,
                socketId: socket.id 
            });
        } catch (error) {
            logger.error('Socket authentication error:', error);
            socket.emit('auth_error', { error: 'Authentication failed' });
        }
    }

    async handleBotAuthentication(socket, data) {
        try {
            const { token } = data;
            
            if (!token) {
                socket.emit('bot_auth_error', { error: 'Bot token required' });
                return;
            }

            // Get bot token from database
            const db = getDatabase();
            const botToken = await db.get(`
                SELECT bt.*, b.id as bot_id, b.name as bot_name, b.user_id, b.is_active as bot_active
                FROM bot_tokens bt
                JOIN bots b ON bt.bot_id = b.id
                WHERE bt.token = ? AND bt.is_active = 1
            `, [token]);

            if (!botToken || !botToken.bot_active) {
                socket.emit('bot_auth_error', { error: 'Invalid bot token' });
                return;
            }

            // Check token expiration
            if (botToken.expires_at && new Date(botToken.expires_at) < new Date()) {
                socket.emit('bot_auth_error', { error: 'Bot token expired' });
                return;
            }

            // Store bot connection
            socket.botId = botToken.bot_id;
            socket.botName = botToken.bot_name;
            socket.botUserId = botToken.user_id;
            this.connectedBots.set(botToken.bot_id, socket);
            this.botSockets.set(socket.id, botToken.bot_id);

            // Update last used timestamp
            await db.run(
                'UPDATE bot_tokens SET last_used_at = CURRENT_TIMESTAMP WHERE id = ?',
                [botToken.id]
            );

            socket.emit('bot_authenticated', { 
                bot: { 
                    id: botToken.bot_id, 
                    name: botToken.bot_name 
                } 
            });

            // Notify bot owner
            const ownerSocket = this.connectedUsers.get(botToken.user_id);
            if (ownerSocket) {
                ownerSocket.emit('bot_connected', { 
                    botId: botToken.bot_id, 
                    botName: botToken.bot_name 
                });
            }

            logger.info('Bot authenticated via socket', { 
                botId: botToken.bot_id, 
                botName: botToken.bot_name,
                socketId: socket.id 
            });
        } catch (error) {
            logger.error('Bot socket authentication error:', error);
            socket.emit('bot_auth_error', { error: 'Bot authentication failed' });
        }
    }

    async handleJoinBotRoom(socket, data) {
        try {
            if (!socket.userId) {
                socket.emit('error', { error: 'Authentication required' });
                return;
            }

            const { botId } = data;
            
            // Verify user owns the bot
            const db = getDatabase();
            const bot = await db.get(
                'SELECT id, name FROM bots WHERE id = ? AND user_id = ?',
                [botId, socket.userId]
            );

            if (!bot) {
                socket.emit('error', { error: 'Bot not found or access denied' });
                return;
            }

            // Join bot room
            const roomName = `bot:${botId}`;
            socket.join(roomName);

            socket.emit('joined_bot_room', { botId, botName: bot.name });

            logger.info('User joined bot room', { 
                userId: socket.userId, 
                botId, 
                socketId: socket.id 
            });
        } catch (error) {
            logger.error('Join bot room error:', error);
            socket.emit('error', { error: 'Failed to join bot room' });
        }
    }

    async handleLeaveBotRoom(socket, data) {
        try {
            const { botId } = data;
            const roomName = `bot:${botId}`;
            
            socket.leave(roomName);
            socket.emit('left_bot_room', { botId });

            logger.info('User left bot room', { 
                userId: socket.userId, 
                botId, 
                socketId: socket.id 
            });
        } catch (error) {
            logger.error('Leave bot room error:', error);
            socket.emit('error', { error: 'Failed to leave bot room' });
        }
    }

    async handleExecuteBotCode(socket, data) {
        try {
            if (!socket.userId) {
                socket.emit('error', { error: 'Authentication required' });
                return;
            }

            const { botId, code, input, timeout } = data;
            
            // Verify user owns the bot
            const db = getDatabase();
            const bot = await db.get(
                'SELECT id, language FROM bots WHERE id = ? AND user_id = ?',
                [botId, socket.userId]
            );

            if (!bot) {
                socket.emit('error', { error: 'Bot not found or access denied' });
                return;
            }

            // Execute code
            const result = await this.botRuntime.executeCode({
                botId,
                code,
                input,
                timeout: timeout || 10000,
                language: bot.language
            });

            // Emit result to user
            socket.emit('bot_execution_result', {
                botId,
                executionId: result.executionId,
                status: result.status,
                output: result.output,
                error: result.error,
                duration: result.duration
            });

            // Emit to bot room
            this.io.to(`bot:${botId}`).emit('bot_execution_update', {
                botId,
                executionId: result.executionId,
                status: result.status,
                duration: result.duration
            });

        } catch (error) {
            logger.error('Execute bot code error:', error);
            socket.emit('error', { error: 'Code execution failed' });
        }
    }

    async handleSendMessageToBot(socket, data) {
        try {
            if (!socket.userId) {
                socket.emit('error', { error: 'Authentication required' });
                return;
            }

            const { botId, content, metadata } = data;
            
            // Verify user owns the bot
            const db = getDatabase();
            const bot = await db.get(
                'SELECT id FROM bots WHERE id = ? AND user_id = ?',
                [botId, socket.userId]
            );

            if (!bot) {
                socket.emit('error', { error: 'Bot not found or access denied' });
                return;
            }

            // Store message
            const result = await db.run(`
                INSERT INTO messages (bot_id, message_type, content, metadata)
                VALUES (?, ?, ?, ?)
            `, [botId, 'incoming', content, JSON.stringify(metadata || {})]);

            const message = {
                id: result.id,
                botId,
                type: 'incoming',
                content,
                metadata: metadata || {},
                timestamp: new Date().toISOString()
            };

            // Send to bot if connected
            const botSocket = this.connectedBots.get(botId);
            if (botSocket) {
                botSocket.emit('message', message);
            }

            // Emit to bot room
            this.io.to(`bot:${botId}`).emit('message_sent', message);

            socket.emit('message_sent', { messageId: result.id });

        } catch (error) {
            logger.error('Send message to bot error:', error);
            socket.emit('error', { error: 'Failed to send message' });
        }
    }

    async handleBotMessage(socket, data) {
        try {
            if (!socket.botId) {
                socket.emit('error', { error: 'Bot authentication required' });
                return;
            }

            const { content, metadata } = data;
            
            // Store message
            const db = getDatabase();
            const result = await db.run(`
                INSERT INTO messages (bot_id, message_type, content, metadata)
                VALUES (?, ?, ?, ?)
            `, [socket.botId, 'outgoing', content, JSON.stringify(metadata || {})]);

            const message = {
                id: result.id,
                botId: socket.botId,
                type: 'outgoing',
                content,
                metadata: metadata || {},
                timestamp: new Date().toISOString()
            };

            // Emit to bot room
            this.io.to(`bot:${socket.botId}`).emit('bot_message', message);

            // Notify bot owner
            const ownerSocket = this.connectedUsers.get(socket.botUserId);
            if (ownerSocket) {
                ownerSocket.emit('bot_message', message);
            }

        } catch (error) {
            logger.error('Bot message error:', error);
            socket.emit('error', { error: 'Failed to send message' });
        }
    }

    async handleBotStatusUpdate(socket, data) {
        try {
            if (!socket.botId) {
                socket.emit('error', { error: 'Bot authentication required' });
                return;
            }

            const { status, metadata } = data;

            // Emit to bot room
            this.io.to(`bot:${socket.botId}`).emit('bot_status_update', {
                botId: socket.botId,
                status,
                metadata: metadata || {},
                timestamp: new Date().toISOString()
            });

            // Notify bot owner
            const ownerSocket = this.connectedUsers.get(socket.botUserId);
            if (ownerSocket) {
                ownerSocket.emit('bot_status_update', {
                    botId: socket.botId,
                    status,
                    metadata: metadata || {},
                    timestamp: new Date().toISOString()
                });
            }

        } catch (error) {
            logger.error('Bot status update error:', error);
            socket.emit('error', { error: 'Failed to update status' });
        }
    }

    handleDisconnection(socket) {
        // Clean up user connection
        if (socket.userId) {
            this.connectedUsers.delete(socket.userId);
            this.userSockets.delete(socket.id);
            logger.info('User disconnected', { userId: socket.userId, socketId: socket.id });
        }

        // Clean up bot connection
        if (socket.botId) {
            this.connectedBots.delete(socket.botId);
            this.botSockets.delete(socket.id);
            
            // Notify bot owner
            const ownerSocket = this.connectedUsers.get(socket.botUserId);
            if (ownerSocket) {
                ownerSocket.emit('bot_disconnected', { 
                    botId: socket.botId, 
                    botName: socket.botName 
                });
            }

            logger.info('Bot disconnected', { botId: socket.botId, socketId: socket.id });
        }
    }

    async subscribeToRedisChannels() {
        try {
            const redis = getRedis();
            
            // Subscribe to bot message channels
            await redis.subscribe('bot:*:messages', (message) => {
                // Forward Redis messages to appropriate socket rooms
                if (message.botId) {
                    this.io.to(`bot:${message.botId}`).emit('bot_message', message);
                }
            });

            logger.info('Subscribed to Redis channels');
        } catch (error) {
            logger.error('Redis subscription error:', error);
        }
    }

    // Utility methods
    getUserSocket(userId) {
        return this.connectedUsers.get(userId);
    }

    getBotSocket(botId) {
        return this.connectedBots.get(botId);
    }

    getConnectedUsers() {
        return Array.from(this.connectedUsers.keys());
    }

    getConnectedBots() {
        return Array.from(this.connectedBots.keys());
    }

    broadcastToAllUsers(event, data) {
        this.connectedUsers.forEach(socket => {
            socket.emit(event, data);
        });
    }

    broadcastToBotOwners(event, data) {
        this.connectedBots.forEach(socket => {
            const ownerSocket = this.connectedUsers.get(socket.botUserId);
            if (ownerSocket) {
                ownerSocket.emit(event, data);
            }
        });
    }
}

module.exports = SocketManager;
