const request = require('supertest');
const BotPlatformServer = require('../server');

describe('Bot Platform Setup', () => {
    let server;
    let app;

    beforeAll(async () => {
        // Set test environment
        process.env.NODE_ENV = 'test';
        process.env.DB_PATH = ':memory:'; // Use in-memory database for tests
        process.env.JWT_SECRET = 'test-secret';
        
        // Create server instance
        server = new BotPlatformServer();
        await server.initialize();
        app = server.app;
    });

    afterAll(async () => {
        if (server) {
            await server.shutdown();
        }
    });

    describe('Health Check', () => {
        test('should return healthy status', async () => {
            const response = await request(app)
                .get('/health')
                .expect(200);

            expect(response.body).toHaveProperty('status', 'healthy');
            expect(response.body).toHaveProperty('timestamp');
            expect(response.body).toHaveProperty('uptime');
        });
    });

    describe('Authentication', () => {
        test('should register a new user', async () => {
            const userData = {
                username: 'testuser',
                email: '<EMAIL>',
                password: 'testpassword123'
            };

            const response = await request(app)
                .post('/api/auth/register')
                .send(userData)
                .expect(201);

            expect(response.body).toHaveProperty('message', 'User registered successfully');
            expect(response.body).toHaveProperty('user');
            expect(response.body).toHaveProperty('token');
            expect(response.body.user).toHaveProperty('username', userData.username);
            expect(response.body.user).toHaveProperty('email', userData.email);
        });

        test('should login with valid credentials', async () => {
            const loginData = {
                email: '<EMAIL>',
                password: 'testpassword123'
            };

            const response = await request(app)
                .post('/api/auth/login')
                .send(loginData)
                .expect(200);

            expect(response.body).toHaveProperty('message', 'Login successful');
            expect(response.body).toHaveProperty('user');
            expect(response.body).toHaveProperty('token');
        });

        test('should reject invalid credentials', async () => {
            const loginData = {
                email: '<EMAIL>',
                password: 'wrongpassword'
            };

            const response = await request(app)
                .post('/api/auth/login')
                .send(loginData)
                .expect(401);

            expect(response.body).toHaveProperty('error', 'Invalid credentials');
        });
    });

    describe('Bot Management', () => {
        let authToken;
        let botId;

        beforeAll(async () => {
            // Login to get auth token
            const loginResponse = await request(app)
                .post('/api/auth/login')
                .send({
                    email: '<EMAIL>',
                    password: 'testpassword123'
                });
            
            authToken = loginResponse.body.token;
        });

        test('should create a new bot', async () => {
            const botData = {
                name: 'Test Bot',
                description: 'A test bot',
                language: 'javascript'
            };

            const response = await request(app)
                .post('/api/bots')
                .set('Authorization', `Bearer ${authToken}`)
                .send(botData)
                .expect(201);

            expect(response.body).toHaveProperty('message', 'Bot created successfully');
            expect(response.body).toHaveProperty('bot');
            expect(response.body.bot).toHaveProperty('name', botData.name);
            
            botId = response.body.bot.id;
        });

        test('should get user bots', async () => {
            const response = await request(app)
                .get('/api/bots')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);

            expect(response.body).toHaveProperty('bots');
            expect(response.body).toHaveProperty('pagination');
            expect(Array.isArray(response.body.bots)).toBe(true);
            expect(response.body.bots.length).toBeGreaterThan(0);
        });

        test('should update bot code', async () => {
            const updateData = {
                code: 'console.log("Hello, World!");'
            };

            const response = await request(app)
                .put(`/api/bots/${botId}`)
                .set('Authorization', `Bearer ${authToken}`)
                .send(updateData)
                .expect(200);

            expect(response.body).toHaveProperty('message', 'Bot updated successfully');
            expect(response.body.bot).toHaveProperty('code', updateData.code);
        });

        test('should create bot token', async () => {
            const tokenData = {
                name: 'Test Token',
                permissions: {
                    execute: true,
                    message: true
                }
            };

            const response = await request(app)
                .post(`/api/bots/${botId}/tokens`)
                .set('Authorization', `Bearer ${authToken}`)
                .send(tokenData)
                .expect(201);

            expect(response.body).toHaveProperty('message', 'Bot token created successfully');
            expect(response.body).toHaveProperty('token');
            expect(response.body.token).toHaveProperty('name', tokenData.name);
        });
    });

    describe('API Endpoints', () => {
        test('should return execution statistics', async () => {
            const response = await request(app)
                .get('/api/stats/executions')
                .expect(200);

            expect(response.body).toHaveProperty('timeframe');
            expect(response.body).toHaveProperty('stats');
            expect(response.body.stats).toHaveProperty('totalExecutions');
            expect(response.body.stats).toHaveProperty('successRate');
        });

        test('should return API health status', async () => {
            const response = await request(app)
                .get('/api/health')
                .expect(200);

            expect(response.body).toHaveProperty('status', 'healthy');
            expect(response.body).toHaveProperty('services');
        });
    });

    describe('Error Handling', () => {
        test('should return 404 for non-existent routes', async () => {
            const response = await request(app)
                .get('/api/nonexistent')
                .expect(404);

            expect(response.body).toHaveProperty('error', 'Route not found');
        });

        test('should require authentication for protected routes', async () => {
            const response = await request(app)
                .get('/api/bots')
                .expect(401);

            expect(response.body).toHaveProperty('error', 'Access token required');
        });

        test('should validate input data', async () => {
            const invalidData = {
                username: 'a', // Too short
                email: 'invalid-email',
                password: '123' // Too short
            };

            const response = await request(app)
                .post('/api/auth/register')
                .send(invalidData)
                .expect(400);

            expect(response.body).toHaveProperty('error');
        });
    });
});
