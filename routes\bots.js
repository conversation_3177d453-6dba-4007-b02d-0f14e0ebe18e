const express = require('express');
const Joi = require('joi');
const { v4: uuidv4 } = require('uuid');
const { getDatabase } = require('../config/database');
const { getRedis } = require('../config/redis');
const { authenticateToken, requireRole } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// All routes require authentication
router.use(authenticateToken);

// Validation schemas
const createBotSchema = Joi.object({
    name: Joi.string().min(1).max(100).required(),
    description: Joi.string().max(500).allow(''),
    language: Joi.string().valid('javascript', 'python').default('javascript')
});

const updateBotSchema = Joi.object({
    name: Joi.string().min(1).max(100),
    description: Joi.string().max(500).allow(''),
    language: Joi.string().valid('javascript', 'python'),
    code: Joi.string().max(100000), // 100KB limit
    is_active: Joi.boolean()
});

const createTokenSchema = Joi.object({
    name: Joi.string().min(1).max(100).required(),
    permissions: Joi.object().default({}),
    expires_at: Joi.date().iso().greater('now').allow(null)
});

// Get all bots for the authenticated user
router.get('/', async (req, res) => {
    try {
        const db = getDatabase();
        const page = parseInt(req.query.page) || 1;
        const limit = Math.min(parseInt(req.query.limit) || 10, 50);
        const offset = (page - 1) * limit;

        // Get bots with pagination
        const bots = await db.all(`
            SELECT id, name, description, language, is_active, is_deployed, created_at, updated_at
            FROM bots 
            WHERE user_id = ? 
            ORDER BY created_at DESC 
            LIMIT ? OFFSET ?
        `, [req.user.id, limit, offset]);

        // Get total count
        const countResult = await db.get(
            'SELECT COUNT(*) as total FROM bots WHERE user_id = ?',
            [req.user.id]
        );

        const total = countResult.total;
        const totalPages = Math.ceil(total / limit);

        res.json({
            bots,
            pagination: {
                page,
                limit,
                total,
                totalPages,
                hasNext: page < totalPages,
                hasPrev: page > 1
            }
        });
    } catch (error) {
        logger.error('Get bots error:', error);
        res.status(500).json({ error: 'Failed to fetch bots' });
    }
});

// Get a specific bot
router.get('/:id', async (req, res) => {
    try {
        const db = getDatabase();
        const bot = await db.get(`
            SELECT * FROM bots 
            WHERE id = ? AND user_id = ?
        `, [req.params.id, req.user.id]);

        if (!bot) {
            return res.status(404).json({ error: 'Bot not found' });
        }

        res.json({ bot });
    } catch (error) {
        logger.error('Get bot error:', error);
        res.status(500).json({ error: 'Failed to fetch bot' });
    }
});

// Create a new bot
router.post('/', async (req, res) => {
    try {
        const { error, value } = createBotSchema.validate(req.body);
        if (error) {
            return res.status(400).json({ error: error.details[0].message });
        }

        const { name, description, language } = value;
        const db = getDatabase();

        // Check if user already has a bot with this name
        const existingBot = await db.get(
            'SELECT id FROM bots WHERE user_id = ? AND name = ?',
            [req.user.id, name]
        );

        if (existingBot) {
            return res.status(409).json({ error: 'Bot with this name already exists' });
        }

        // Create bot
        const result = await db.run(`
            INSERT INTO bots (user_id, name, description, language, code)
            VALUES (?, ?, ?, ?, ?)
        `, [req.user.id, name, description || '', language, '// Your bot code here']);

        const botId = result.id;

        // Get created bot
        const bot = await db.get('SELECT * FROM bots WHERE id = ?', [botId]);

        logger.info('Bot created', { userId: req.user.id, botId, name });

        res.status(201).json({
            message: 'Bot created successfully',
            bot
        });
    } catch (error) {
        logger.error('Create bot error:', error);
        res.status(500).json({ error: 'Failed to create bot' });
    }
});

// Update a bot
router.put('/:id', async (req, res) => {
    try {
        const { error, value } = updateBotSchema.validate(req.body);
        if (error) {
            return res.status(400).json({ error: error.details[0].message });
        }

        const db = getDatabase();
        const botId = req.params.id;

        // Check if bot exists and belongs to user
        const existingBot = await db.get(
            'SELECT * FROM bots WHERE id = ? AND user_id = ?',
            [botId, req.user.id]
        );

        if (!existingBot) {
            return res.status(404).json({ error: 'Bot not found' });
        }

        const updates = [];
        const params = [];

        // Build dynamic update query
        Object.entries(value).forEach(([key, val]) => {
            if (val !== undefined) {
                updates.push(`${key} = ?`);
                params.push(val);
            }
        });

        if (updates.length === 0) {
            return res.status(400).json({ error: 'No valid fields to update' });
        }

        updates.push('updated_at = CURRENT_TIMESTAMP');
        params.push(botId);

        await db.run(
            `UPDATE bots SET ${updates.join(', ')} WHERE id = ?`,
            params
        );

        // Get updated bot
        const updatedBot = await db.get('SELECT * FROM bots WHERE id = ?', [botId]);

        // If code was updated and bot is deployed, mark as needing redeployment
        if (value.code && existingBot.is_deployed) {
            await db.run(
                'UPDATE bots SET is_deployed = 0 WHERE id = ?',
                [botId]
            );
            updatedBot.is_deployed = 0;
        }

        logger.info('Bot updated', { 
            userId: req.user.id, 
            botId, 
            updates: Object.keys(value) 
        });

        res.json({
            message: 'Bot updated successfully',
            bot: updatedBot
        });
    } catch (error) {
        logger.error('Update bot error:', error);
        res.status(500).json({ error: 'Failed to update bot' });
    }
});

// Delete a bot
router.delete('/:id', async (req, res) => {
    try {
        const db = getDatabase();
        const botId = req.params.id;

        // Check if bot exists and belongs to user
        const bot = await db.get(
            'SELECT * FROM bots WHERE id = ? AND user_id = ?',
            [botId, req.user.id]
        );

        if (!bot) {
            return res.status(404).json({ error: 'Bot not found' });
        }

        // Delete bot (cascade will handle related records)
        await db.run('DELETE FROM bots WHERE id = ?', [botId]);

        // Clear bot state from Redis
        const redis = getRedis();
        await redis.deleteBotState(botId);

        logger.info('Bot deleted', { userId: req.user.id, botId, name: bot.name });

        res.json({ message: 'Bot deleted successfully' });
    } catch (error) {
        logger.error('Delete bot error:', error);
        res.status(500).json({ error: 'Failed to delete bot' });
    }
});

// Get bot tokens
router.get('/:id/tokens', async (req, res) => {
    try {
        const db = getDatabase();
        const botId = req.params.id;

        // Check if bot exists and belongs to user
        const bot = await db.get(
            'SELECT id FROM bots WHERE id = ? AND user_id = ?',
            [botId, req.user.id]
        );

        if (!bot) {
            return res.status(404).json({ error: 'Bot not found' });
        }

        // Get tokens (excluding the actual token value for security)
        const tokens = await db.all(`
            SELECT id, name, permissions, is_active, expires_at, last_used_at, created_at
            FROM bot_tokens 
            WHERE bot_id = ? 
            ORDER BY created_at DESC
        `, [botId]);

        // Parse permissions JSON
        tokens.forEach(token => {
            if (token.permissions) {
                try {
                    token.permissions = JSON.parse(token.permissions);
                } catch {
                    token.permissions = {};
                }
            } else {
                token.permissions = {};
            }
        });

        res.json({ tokens });
    } catch (error) {
        logger.error('Get bot tokens error:', error);
        res.status(500).json({ error: 'Failed to fetch bot tokens' });
    }
});

// Create a new bot token
router.post('/:id/tokens', async (req, res) => {
    try {
        const { error, value } = createTokenSchema.validate(req.body);
        if (error) {
            return res.status(400).json({ error: error.details[0].message });
        }

        const db = getDatabase();
        const botId = req.params.id;

        // Check if bot exists and belongs to user
        const bot = await db.get(
            'SELECT id FROM bots WHERE id = ? AND user_id = ?',
            [botId, req.user.id]
        );

        if (!bot) {
            return res.status(404).json({ error: 'Bot not found' });
        }

        // Generate unique token
        const token = `bot_${uuidv4().replace(/-/g, '')}`;
        
        // Create token record
        const result = await db.run(`
            INSERT INTO bot_tokens (bot_id, token, name, permissions, expires_at)
            VALUES (?, ?, ?, ?, ?)
        `, [
            botId,
            token,
            value.name,
            JSON.stringify(value.permissions),
            value.expires_at
        ]);

        logger.info('Bot token created', { 
            userId: req.user.id, 
            botId, 
            tokenId: result.id,
            tokenName: value.name
        });

        res.status(201).json({
            message: 'Bot token created successfully',
            token: {
                id: result.id,
                token, // Only show the token once during creation
                name: value.name,
                permissions: value.permissions,
                expires_at: value.expires_at
            }
        });
    } catch (error) {
        logger.error('Create bot token error:', error);
        res.status(500).json({ error: 'Failed to create bot token' });
    }
});

// Delete a bot token
router.delete('/:id/tokens/:tokenId', async (req, res) => {
    try {
        const db = getDatabase();
        const botId = req.params.id;
        const tokenId = req.params.tokenId;

        // Check if bot exists and belongs to user
        const bot = await db.get(
            'SELECT id FROM bots WHERE id = ? AND user_id = ?',
            [botId, req.user.id]
        );

        if (!bot) {
            return res.status(404).json({ error: 'Bot not found' });
        }

        // Check if token exists for this bot
        const token = await db.get(
            'SELECT id, name FROM bot_tokens WHERE id = ? AND bot_id = ?',
            [tokenId, botId]
        );

        if (!token) {
            return res.status(404).json({ error: 'Token not found' });
        }

        // Delete token
        await db.run('DELETE FROM bot_tokens WHERE id = ?', [tokenId]);

        logger.info('Bot token deleted', { 
            userId: req.user.id, 
            botId, 
            tokenId,
            tokenName: token.name
        });

        res.json({ message: 'Bot token deleted successfully' });
    } catch (error) {
        logger.error('Delete bot token error:', error);
        res.status(500).json({ error: 'Failed to delete bot token' });
    }
});

module.exports = router;
