require('dotenv').config();
const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const path = require('path');

// Import configurations and middleware
const { initializeDatabase } = require('./config/database');
const { initializeRedis } = require('./config/redis');
const logger = require('./utils/logger');

// Import routes
const authRoutes = require('./routes/auth');
const botRoutes = require('./routes/bots');
const apiRoutes = require('./routes/api');

// Import services
const BotRuntime = require('./services/BotRuntime');
const SocketManager = require('./services/SocketManager');

class BotPlatformServer {
    constructor() {
        this.app = express();
        this.server = http.createServer(this.app);
        this.io = socketIo(this.server, {
            cors: {
                origin: process.env.NODE_ENV === 'production' ? false : "*",
                methods: ["GET", "POST"]
            }
        });
        this.port = process.env.PORT || 3000;
        this.botRuntime = null;
        this.socketManager = null;
    }

    async initialize() {
        try {
            // Initialize database
            await initializeDatabase();
            logger.info('Database initialized successfully');

            // Initialize Redis (optional)
            try {
                await initializeRedis();
                logger.info('Redis initialized successfully');
            } catch (error) {
                logger.warn('Redis not available, continuing without caching:', error.message);
            }

            // Initialize bot runtime
            this.botRuntime = new BotRuntime();
            await this.botRuntime.initialize();
            logger.info('Bot runtime initialized successfully');

            // Initialize socket manager
            this.socketManager = new SocketManager(this.io, this.botRuntime);
            this.socketManager.initialize();
            logger.info('Socket manager initialized successfully');

            // Setup middleware
            this.setupMiddleware();

            // Setup routes
            this.setupRoutes();

            // Setup error handling
            this.setupErrorHandling();

            logger.info('Server initialization completed');
        } catch (error) {
            logger.error('Failed to initialize server:', error);
            process.exit(1);
        }
    }

    setupMiddleware() {
        // Security middleware
        this.app.use(helmet());
        
        // CORS
        this.app.use(cors({
            origin: process.env.NODE_ENV === 'production' ? false : "*",
            credentials: true
        }));

        // Rate limiting
        const limiter = rateLimit({
            windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000,
            max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
            message: 'Too many requests from this IP, please try again later.'
        });
        this.app.use('/api/', limiter);

        // Body parsing
        this.app.use(express.json({ limit: '10mb' }));
        this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

        // Logging middleware
        this.app.use((req, res, next) => {
            logger.info(`${req.method} ${req.path} - ${req.ip}`);
            next();
        });
    }

    setupRoutes() {
        // Health check
        this.app.get('/health', (req, res) => {
            res.json({ 
                status: 'healthy', 
                timestamp: new Date().toISOString(),
                uptime: process.uptime()
            });
        });

        // API routes
        this.app.use('/api/auth', authRoutes);
        this.app.use('/api/bots', botRoutes);
        this.app.use('/api', apiRoutes);

        // Serve static files (for future web interface)
        this.app.use(express.static(path.join(__dirname, 'public')));

        // 404 handler
        this.app.use('*', (req, res) => {
            res.status(404).json({ error: 'Route not found' });
        });
    }

    setupErrorHandling() {
        // Global error handler
        this.app.use((error, req, res, next) => {
            logger.error('Unhandled error:', error);
            
            if (res.headersSent) {
                return next(error);
            }

            const statusCode = error.statusCode || 500;
            const message = process.env.NODE_ENV === 'production' 
                ? 'Internal server error' 
                : error.message;

            res.status(statusCode).json({
                error: message,
                ...(process.env.NODE_ENV !== 'production' && { stack: error.stack })
            });
        });

        // Handle unhandled promise rejections
        process.on('unhandledRejection', (reason, promise) => {
            logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
        });

        // Handle uncaught exceptions
        process.on('uncaughtException', (error) => {
            logger.error('Uncaught Exception:', error);
            process.exit(1);
        });
    }

    async start() {
        await this.initialize();
        
        this.server.listen(this.port, () => {
            logger.info(`Bot Platform Server running on port ${this.port}`);
            logger.info(`Environment: ${process.env.NODE_ENV}`);
        });
    }

    async shutdown() {
        logger.info('Shutting down server...');
        
        if (this.botRuntime) {
            await this.botRuntime.shutdown();
        }
        
        this.server.close(() => {
            logger.info('Server shutdown complete');
            process.exit(0);
        });
    }
}

// Handle graceful shutdown
process.on('SIGTERM', async () => {
    if (global.server) {
        await global.server.shutdown();
    }
});

process.on('SIGINT', async () => {
    if (global.server) {
        await global.server.shutdown();
    }
});

// Start server
if (require.main === module) {
    const server = new BotPlatformServer();
    global.server = server;
    server.start().catch(error => {
        logger.error('Failed to start server:', error);
        process.exit(1);
    });
}

module.exports = BotPlatformServer;
