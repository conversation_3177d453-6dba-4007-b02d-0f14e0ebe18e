#!/usr/bin/env node

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

class BotTokenManager {
    constructor() {
        this.authToken = null;
        this.botId = null;
        this.botToken = null;
    }

    async makeRequest(method, endpoint, data = null, headers = {}) {
        try {
            const config = {
                method,
                url: `${BASE_URL}${endpoint}`,
                headers: {
                    'Content-Type': 'application/json',
                    ...headers
                }
            };

            if (data) {
                config.data = data;
            }

            const response = await axios(config);
            return response.data;
        } catch (error) {
            console.error(`❌ Error ${method} ${endpoint}:`, error.response?.data || error.message);
            throw error;
        }
    }

    async login(email = '<EMAIL>', password = 'testpassword123') {
        console.log('🔑 Logging in...');
        const result = await this.makeRequest('POST', '/api/auth/login', {
            email,
            password
        });
        
        this.authToken = result.token;
        console.log(`✅ Logged in as: ${result.user.username}`);
        return result;
    }

    async createBot(name = 'Test Bot', description = 'A test bot for token generation') {
        console.log('🤖 Creating bot...');
        const result = await this.makeRequest('POST', '/api/bots', {
            name,
            description,
            language: 'javascript'
        }, {
            'Authorization': `Bearer ${this.authToken}`
        });
        
        this.botId = result.bot.id;
        console.log(`✅ Bot created: ${result.bot.name} (ID: ${this.botId})`);
        return result;
    }

    async createBotToken(tokenName = 'API Token') {
        console.log('🎫 Creating bot token...');
        const result = await this.makeRequest('POST', `/api/bots/${this.botId}/tokens`, {
            name: tokenName,
            permissions: {
                execute: true,
                message: true,
                state: true
            }
        }, {
            'Authorization': `Bearer ${this.authToken}`
        });
        
        this.botToken = result.token.token;
        console.log(`✅ Bot token created: ${result.token.name}`);
        return result;
    }

    async getBotToken(email, password, botName, tokenName) {
        try {
            console.log('🚀 Bot Token Generation Process');
            console.log('================================\n');

            // Step 1: Login
            await this.login(email, password);

            // Step 2: Create Bot
            await this.createBot(botName);

            // Step 3: Create Bot Token
            await this.createBotToken(tokenName);

            // Display results
            console.log('\n🎉 Success! Bot token generated');
            console.log('================================');
            console.log(`📋 Bot ID: ${this.botId}`);
            console.log(`🎫 Bot Token: ${this.botToken}`);
            
            console.log('\n📝 Usage Examples:');
            console.log('   # Test bot API with token:');
            console.log(`   curl -H "Authorization: Bearer ${this.botToken}" http://localhost:3000/api/bot/state`);
            
            console.log('\n   # Execute code with bot:');
            console.log(`   curl -X POST http://localhost:3000/api/bot/execute \\`);
            console.log(`     -H "Authorization: Bearer ${this.botToken}" \\`);
            console.log(`     -H "Content-Type: application/json" \\`);
            console.log(`     -d '{"code":"console.log(\\"Hello World\\");","timeout":10000}'`);
            
            console.log('\n   # Send message as bot:');
            console.log(`   curl -X POST http://localhost:3000/api/bot/message \\`);
            console.log(`     -H "Authorization: Bearer ${this.botToken}" \\`);
            console.log(`     -H "Content-Type: application/json" \\`);
            console.log(`     -d '{"content":"Hello from bot!","metadata":{}}'`);

            console.log('\n   # Save token to environment variable:');
            console.log(`   export BOT_TOKEN="${this.botToken}"`);

            return {
                botId: this.botId,
                botToken: this.botToken,
                authToken: this.authToken
            };

        } catch (error) {
            console.error('\n💥 Failed to generate bot token:', error.message);
            
            if (error.response?.status === 401) {
                console.log('\n💡 Suggestions:');
                console.log('   - Check your email/password');
                console.log('   - Register a new user first:');
                console.log(`     curl -X POST ${BASE_URL}/api/auth/register -H "Content-Type: application/json" -d '{"username":"newuser","email":"<EMAIL>","password":"newpassword123"}'`);
            }
            
            process.exit(1);
        }
    }

    async listExistingBots() {
        try {
            console.log('📋 Listing existing bots...');
            const result = await this.makeRequest('GET', '/api/bots', null, {
                'Authorization': `Bearer ${this.authToken}`
            });
            
            if (result.bots.length === 0) {
                console.log('   No bots found');
                return [];
            }
            
            console.log(`   Found ${result.bots.length} bot(s):`);
            result.bots.forEach(bot => {
                console.log(`   - ${bot.name} (ID: ${bot.id}) - ${bot.language}`);
            });
            
            return result.bots;
        } catch (error) {
            console.log('   Could not list bots');
            return [];
        }
    }

    async getTokenForExistingBot(botId, tokenName = 'API Token') {
        try {
            console.log(`🎫 Creating token for existing bot (ID: ${botId})...`);
            this.botId = botId;
            
            const result = await this.createBotToken(tokenName);
            
            console.log('\n🎉 Success! Bot token generated for existing bot');
            console.log('================================');
            console.log(`📋 Bot ID: ${this.botId}`);
            console.log(`🎫 Bot Token: ${this.botToken}`);
            
            return {
                botId: this.botId,
                botToken: this.botToken
            };
            
        } catch (error) {
            console.error('💥 Failed to create token for existing bot:', error.message);
            throw error;
        }
    }
}

// Command line usage
if (require.main === module) {
    const args = process.argv.slice(2);
    const email = args[0] || '<EMAIL>';
    const password = args[1] || 'testpassword123';
    const botName = args[2] || `Test Bot ${Date.now()}`;
    const tokenName = args[3] || 'API Token';
    
    const manager = new BotTokenManager();
    
    // Check if user wants to use existing bot
    if (args.includes('--list')) {
        manager.login(email, password)
            .then(() => manager.listExistingBots())
            .then(() => {
                console.log('\n💡 To create token for existing bot:');
                console.log('   node get-bot-token.js email password --existing BOT_ID');
            });
    } else if (args.includes('--existing')) {
        const botId = args[args.indexOf('--existing') + 1];
        if (!botId) {
            console.log('❌ Please provide bot ID after --existing');
            console.log('   Usage: node get-bot-token.js email password --existing BOT_ID');
            process.exit(1);
        }
        
        manager.login(email, password)
            .then(() => manager.getTokenForExistingBot(parseInt(botId), tokenName));
    } else {
        manager.getBotToken(email, password, botName, tokenName);
    }
}

module.exports = BotTokenManager;
