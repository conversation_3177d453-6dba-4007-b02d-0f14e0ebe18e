const jwt = require('jsonwebtoken');
const { getDatabase } = require('../config/database');
const { getRedis } = require('../config/redis');
const logger = require('../utils/logger');

// JWT Authentication Middleware
const authenticateToken = async (req, res, next) => {
    try {
        const authHeader = req.headers['authorization'];
        const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

        if (!token) {
            return res.status(401).json({ error: 'Access token required' });
        }

        // Check if token is blacklisted
        const redis = getRedis();
        const isBlacklisted = await redis.exists(`blacklist:${token}`);
        if (isBlacklisted) {
            return res.status(401).json({ error: 'Token has been revoked' });
        }

        // Verify token
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        
        // Get user from database
        const db = getDatabase();
        const user = await db.get(
            'SELECT id, username, email, role, is_active FROM users WHERE id = ?',
            [decoded.userId]
        );

        if (!user) {
            return res.status(401).json({ error: 'User not found' });
        }

        if (!user.is_active) {
            return res.status(401).json({ error: 'Account is deactivated' });
        }

        // Add user to request object
        req.user = user;
        req.token = token;
        
        next();
    } catch (error) {
        if (error.name === 'JsonWebTokenError') {
            return res.status(401).json({ error: 'Invalid token' });
        }
        if (error.name === 'TokenExpiredError') {
            return res.status(401).json({ error: 'Token expired' });
        }
        
        logger.error('Authentication error:', error);
        return res.status(500).json({ error: 'Authentication failed' });
    }
};

// Bot Token Authentication Middleware
const authenticateBotToken = async (req, res, next) => {
    try {
        const authHeader = req.headers['authorization'];
        const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

        if (!token) {
            return res.status(401).json({ error: 'Bot token required' });
        }

        // Get bot token from database
        const db = getDatabase();
        const botToken = await db.get(`
            SELECT bt.*, b.id as bot_id, b.name as bot_name, b.user_id, b.is_active as bot_active
            FROM bot_tokens bt
            JOIN bots b ON bt.bot_id = b.id
            WHERE bt.token = ? AND bt.is_active = 1
        `, [token]);

        if (!botToken) {
            return res.status(401).json({ error: 'Invalid bot token' });
        }

        if (!botToken.bot_active) {
            return res.status(401).json({ error: 'Bot is not active' });
        }

        // Check token expiration
        if (botToken.expires_at && new Date(botToken.expires_at) < new Date()) {
            return res.status(401).json({ error: 'Bot token expired' });
        }

        // Update last used timestamp
        await db.run(
            'UPDATE bot_tokens SET last_used_at = CURRENT_TIMESTAMP WHERE id = ?',
            [botToken.id]
        );

        // Add bot info to request object
        req.bot = {
            id: botToken.bot_id,
            name: botToken.bot_name,
            userId: botToken.user_id,
            tokenId: botToken.id,
            permissions: botToken.permissions ? JSON.parse(botToken.permissions) : {}
        };
        req.botToken = token;
        
        next();
    } catch (error) {
        logger.error('Bot token authentication error:', error);
        return res.status(500).json({ error: 'Bot authentication failed' });
    }
};

// Role-based Authorization Middleware
const requireRole = (roles) => {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({ error: 'Authentication required' });
        }

        const userRole = req.user.role;
        const allowedRoles = Array.isArray(roles) ? roles : [roles];

        if (!allowedRoles.includes(userRole)) {
            logger.logSecurityEvent('unauthorized_access_attempt', {
                userId: req.user.id,
                userRole,
                requiredRoles: allowedRoles,
                path: req.path
            });
            return res.status(403).json({ error: 'Insufficient permissions' });
        }

        next();
    };
};

// Bot Permission Middleware
const requireBotPermission = (permission) => {
    return (req, res, next) => {
        if (!req.bot) {
            return res.status(401).json({ error: 'Bot authentication required' });
        }

        const permissions = req.bot.permissions;
        
        if (!permissions[permission]) {
            logger.logSecurityEvent('bot_permission_denied', {
                botId: req.bot.id,
                permission,
                path: req.path
            });
            return res.status(403).json({ error: `Bot lacks permission: ${permission}` });
        }

        next();
    };
};

// Rate Limiting Middleware for Bot API
const rateLimitBot = async (req, res, next) => {
    try {
        if (!req.bot) {
            return next();
        }

        const redis = getRedis();
        const key = `rate_limit:bot:${req.bot.id}`;
        const limit = 100; // requests per minute
        const window = 60; // seconds

        const current = await redis.incrementCounter(key, window);
        
        if (current > limit) {
            logger.logSecurityEvent('bot_rate_limit_exceeded', {
                botId: req.bot.id,
                current,
                limit
            });
            return res.status(429).json({ 
                error: 'Rate limit exceeded',
                retryAfter: window
            });
        }

        // Add rate limit headers
        res.set({
            'X-RateLimit-Limit': limit,
            'X-RateLimit-Remaining': Math.max(0, limit - current),
            'X-RateLimit-Reset': new Date(Date.now() + window * 1000).toISOString()
        });

        next();
    } catch (error) {
        logger.error('Rate limiting error:', error);
        next(); // Continue on error to avoid blocking requests
    }
};

// Optional Authentication Middleware
const optionalAuth = async (req, res, next) => {
    try {
        const authHeader = req.headers['authorization'];
        const token = authHeader && authHeader.split(' ')[1];

        if (!token) {
            return next(); // No token provided, continue without authentication
        }

        // Try to authenticate if token is provided
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const db = getDatabase();
        const user = await db.get(
            'SELECT id, username, email, role, is_active FROM users WHERE id = ?',
            [decoded.userId]
        );

        if (user && user.is_active) {
            req.user = user;
            req.token = token;
        }

        next();
    } catch (error) {
        // Ignore authentication errors for optional auth
        next();
    }
};

module.exports = {
    authenticateToken,
    authenticateBotToken,
    requireRole,
    requireBotPermission,
    rateLimitBot,
    optionalAuth
};
