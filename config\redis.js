const redis = require('redis');
const logger = require('../utils/logger');

class RedisManager {
    constructor() {
        this.client = null;
        this.subscriber = null;
        this.publisher = null;
        this.isConnected = false;
    }

    async initialize() {
        try {
            const redisConfig = {
                host: process.env.REDIS_HOST || 'localhost',
                port: parseInt(process.env.REDIS_PORT) || 6379,
                password: process.env.REDIS_PASSWORD || undefined,
                retryDelayOnFailover: 100,
                enableReadyCheck: true,
                maxRetriesPerRequest: 3,
            };

            // Main client for general operations
            this.client = redis.createClient(redisConfig);
            
            // Subscriber client for pub/sub
            this.subscriber = redis.createClient(redisConfig);
            
            // Publisher client for pub/sub
            this.publisher = redis.createClient(redisConfig);

            // Setup error handlers
            this.client.on('error', (err) => {
                logger.error('Redis client error:', err);
            });

            this.subscriber.on('error', (err) => {
                logger.error('Redis subscriber error:', err);
            });

            this.publisher.on('error', (err) => {
                logger.error('Redis publisher error:', err);
            });

            // Setup connection handlers
            this.client.on('connect', () => {
                logger.info('Redis client connected');
                this.isConnected = true;
            });

            this.client.on('disconnect', () => {
                logger.warn('Redis client disconnected');
                this.isConnected = false;
            });

            // Connect all clients
            await Promise.all([
                this.client.connect(),
                this.subscriber.connect(),
                this.publisher.connect()
            ]);

            logger.info('Redis connections established successfully');
            return this;
        } catch (error) {
            logger.error('Redis initialization failed:', error);
            throw error;
        }
    }

    // Cache operations
    async set(key, value, expireInSeconds = null) {
        try {
            const serializedValue = JSON.stringify(value);
            if (expireInSeconds) {
                await this.client.setEx(key, expireInSeconds, serializedValue);
            } else {
                await this.client.set(key, serializedValue);
            }
            return true;
        } catch (error) {
            logger.error('Redis set error:', error);
            return false;
        }
    }

    async get(key) {
        try {
            const value = await this.client.get(key);
            return value ? JSON.parse(value) : null;
        } catch (error) {
            logger.error('Redis get error:', error);
            return null;
        }
    }

    async del(key) {
        try {
            return await this.client.del(key);
        } catch (error) {
            logger.error('Redis del error:', error);
            return false;
        }
    }

    async exists(key) {
        try {
            return await this.client.exists(key);
        } catch (error) {
            logger.error('Redis exists error:', error);
            return false;
        }
    }

    // Hash operations
    async hSet(key, field, value) {
        try {
            const serializedValue = JSON.stringify(value);
            return await this.client.hSet(key, field, serializedValue);
        } catch (error) {
            logger.error('Redis hSet error:', error);
            return false;
        }
    }

    async hGet(key, field) {
        try {
            const value = await this.client.hGet(key, field);
            return value ? JSON.parse(value) : null;
        } catch (error) {
            logger.error('Redis hGet error:', error);
            return null;
        }
    }

    async hGetAll(key) {
        try {
            const hash = await this.client.hGetAll(key);
            const result = {};
            for (const [field, value] of Object.entries(hash)) {
                try {
                    result[field] = JSON.parse(value);
                } catch {
                    result[field] = value;
                }
            }
            return result;
        } catch (error) {
            logger.error('Redis hGetAll error:', error);
            return {};
        }
    }

    async hDel(key, field) {
        try {
            return await this.client.hDel(key, field);
        } catch (error) {
            logger.error('Redis hDel error:', error);
            return false;
        }
    }

    // List operations
    async lPush(key, value) {
        try {
            const serializedValue = JSON.stringify(value);
            return await this.client.lPush(key, serializedValue);
        } catch (error) {
            logger.error('Redis lPush error:', error);
            return false;
        }
    }

    async rPop(key) {
        try {
            const value = await this.client.rPop(key);
            return value ? JSON.parse(value) : null;
        } catch (error) {
            logger.error('Redis rPop error:', error);
            return null;
        }
    }

    async lRange(key, start, stop) {
        try {
            const values = await this.client.lRange(key, start, stop);
            return values.map(value => {
                try {
                    return JSON.parse(value);
                } catch {
                    return value;
                }
            });
        } catch (error) {
            logger.error('Redis lRange error:', error);
            return [];
        }
    }

    // Pub/Sub operations
    async publish(channel, message) {
        try {
            const serializedMessage = JSON.stringify(message);
            return await this.publisher.publish(channel, serializedMessage);
        } catch (error) {
            logger.error('Redis publish error:', error);
            return false;
        }
    }

    async subscribe(channel, callback) {
        try {
            await this.subscriber.subscribe(channel, (message) => {
                try {
                    const parsedMessage = JSON.parse(message);
                    callback(parsedMessage);
                } catch {
                    callback(message);
                }
            });
            logger.info(`Subscribed to Redis channel: ${channel}`);
        } catch (error) {
            logger.error('Redis subscribe error:', error);
        }
    }

    async unsubscribe(channel) {
        try {
            await this.subscriber.unsubscribe(channel);
            logger.info(`Unsubscribed from Redis channel: ${channel}`);
        } catch (error) {
            logger.error('Redis unsubscribe error:', error);
        }
    }

    // Rate limiting
    async incrementCounter(key, expireInSeconds = 3600) {
        try {
            const multi = this.client.multi();
            multi.incr(key);
            multi.expire(key, expireInSeconds);
            const results = await multi.exec();
            return results[0];
        } catch (error) {
            logger.error('Redis increment counter error:', error);
            return null;
        }
    }

    // Session management
    async setSession(sessionId, sessionData, expireInSeconds = 86400) {
        return await this.set(`session:${sessionId}`, sessionData, expireInSeconds);
    }

    async getSession(sessionId) {
        return await this.get(`session:${sessionId}`);
    }

    async deleteSession(sessionId) {
        return await this.del(`session:${sessionId}`);
    }

    // Bot state management
    async setBotState(botId, state, expireInSeconds = 3600) {
        return await this.set(`bot:${botId}:state`, state, expireInSeconds);
    }

    async getBotState(botId) {
        return await this.get(`bot:${botId}:state`);
    }

    async deleteBotState(botId) {
        return await this.del(`bot:${botId}:state`);
    }

    async close() {
        try {
            await Promise.all([
                this.client?.quit(),
                this.subscriber?.quit(),
                this.publisher?.quit()
            ]);
            logger.info('Redis connections closed');
        } catch (error) {
            logger.error('Error closing Redis connections:', error);
        }
    }

    isHealthy() {
        return this.isConnected;
    }
}

// Singleton instance
let redisInstance = null;

async function initializeRedis() {
    if (!redisInstance) {
        redisInstance = new RedisManager();
        await redisInstance.initialize();
    }
    return redisInstance;
}

function getRedis() {
    if (!redisInstance) {
        // Return a mock Redis instance if Redis is not available
        return {
            set: async () => true,
            get: async () => null,
            del: async () => true,
            exists: async () => false,
            hSet: async () => true,
            hGet: async () => null,
            hGetAll: async () => ({}),
            hDel: async () => true,
            lPush: async () => true,
            rPop: async () => null,
            lRange: async () => [],
            publish: async () => true,
            subscribe: async () => {},
            unsubscribe: async () => {},
            incrementCounter: async () => 1,
            setSession: async () => true,
            getSession: async () => null,
            deleteSession: async () => true,
            setBotState: async () => true,
            getBotState: async () => null,
            deleteBotState: async () => true,
            close: async () => {},
            isHealthy: () => false
        };
    }
    return redisInstance;
}

module.exports = {
    initializeRedis,
    getRedis,
    RedisManager
};
