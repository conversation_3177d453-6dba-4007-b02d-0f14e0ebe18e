const { v4: uuidv4 } = require('uuid');
const DockerSandbox = require('./DockerSandbox');
const { getRedis } = require('../config/redis');
const logger = require('../utils/logger');

class BotRuntime {
    constructor() {
        this.dockerSandbox = new DockerSandbox();
        this.activeExecutions = new Map();
        this.maxConcurrentExecutions = 10;
    }

    async initialize() {
        try {
            await this.dockerSandbox.initialize();
            logger.info('Bot runtime initialized successfully');
        } catch (error) {
            logger.error('Bot runtime initialization failed:', error);
            throw error;
        }
    }

    async executeCode(options) {
        const {
            botId,
            code,
            input = null,
            timeout = 10000,
            language = 'javascript'
        } = options;

        const executionId = uuidv4();
        const startTime = Date.now();

        try {
            // Check concurrent execution limit
            if (this.activeExecutions.size >= this.maxConcurrentExecutions) {
                throw new Error('Maximum concurrent executions reached');
            }

            // Validate code
            this.validateCode(code, language);

            // Add to active executions
            this.activeExecutions.set(executionId, {
                botId,
                startTime,
                timeout
            });

            // Execute in sandbox
            const result = await this.dockerSandbox.execute({
                executionId,
                botId,
                code,
                input,
                timeout,
                language
            });

            const duration = Date.now() - startTime;

            // Remove from active executions
            this.activeExecutions.delete(executionId);

            // Cache execution result
            const redis = getRedis();
            await redis.set(
                `execution:${executionId}`,
                {
                    botId,
                    status: result.status,
                    output: result.output,
                    error: result.error,
                    duration,
                    timestamp: new Date().toISOString()
                },
                3600 // Cache for 1 hour
            );

            return {
                executionId,
                status: result.status,
                output: result.output,
                error: result.error,
                duration,
                memoryUsed: result.memoryUsed,
                cpuUsed: result.cpuUsed
            };
        } catch (error) {
            const duration = Date.now() - startTime;
            
            // Remove from active executions
            this.activeExecutions.delete(executionId);

            logger.logBotError(botId, error, {
                executionId,
                duration,
                language
            });

            return {
                executionId,
                status: 'failed',
                output: null,
                error: error.message,
                duration,
                memoryUsed: null,
                cpuUsed: null
            };
        }
    }

    validateCode(code, language) {
        if (!code || typeof code !== 'string') {
            throw new Error('Code must be a non-empty string');
        }

        if (code.length > 100000) { // 100KB limit
            throw new Error('Code size exceeds maximum limit (100KB)');
        }

        // Basic security checks
        const dangerousPatterns = [
            /require\s*\(\s*['"`]fs['"`]\s*\)/i,
            /require\s*\(\s*['"`]child_process['"`]\s*\)/i,
            /require\s*\(\s*['"`]os['"`]\s*\)/i,
            /require\s*\(\s*['"`]path['"`]\s*\)/i,
            /require\s*\(\s*['"`]net['"`]\s*\)/i,
            /require\s*\(\s*['"`]http['"`]\s*\)/i,
            /require\s*\(\s*['"`]https['"`]\s*\)/i,
            /process\s*\.\s*exit/i,
            /process\s*\.\s*kill/i,
            /eval\s*\(/i,
            /Function\s*\(/i,
            /setTimeout\s*\(/i,
            /setInterval\s*\(/i,
            /while\s*\(\s*true\s*\)/i,
            /for\s*\(\s*;\s*;\s*\)/i
        ];

        for (const pattern of dangerousPatterns) {
            if (pattern.test(code)) {
                throw new Error(`Potentially dangerous code pattern detected: ${pattern.source}`);
            }
        }

        // Language-specific validation
        if (language === 'javascript') {
            this.validateJavaScript(code);
        } else if (language === 'python') {
            this.validatePython(code);
        }
    }

    validateJavaScript(code) {
        // Check for basic syntax errors
        try {
            new Function(code);
        } catch (error) {
            throw new Error(`JavaScript syntax error: ${error.message}`);
        }

        // Additional JavaScript-specific checks
        const jsPatterns = [
            /global\s*\./i,
            /globalThis\s*\./i,
            /window\s*\./i,
            /document\s*\./i,
            /__dirname/i,
            /__filename/i
        ];

        for (const pattern of jsPatterns) {
            if (pattern.test(code)) {
                throw new Error(`Restricted JavaScript feature detected: ${pattern.source}`);
            }
        }
    }

    validatePython(code) {
        // Python-specific dangerous patterns
        const pythonPatterns = [
            /import\s+os/i,
            /import\s+sys/i,
            /import\s+subprocess/i,
            /import\s+socket/i,
            /import\s+urllib/i,
            /import\s+requests/i,
            /from\s+os\s+import/i,
            /from\s+sys\s+import/i,
            /exec\s*\(/i,
            /eval\s*\(/i,
            /compile\s*\(/i,
            /__import__\s*\(/i
        ];

        for (const pattern of pythonPatterns) {
            if (pattern.test(code)) {
                throw new Error(`Restricted Python feature detected: ${pattern.source}`);
            }
        }
    }

    async getExecutionResult(executionId) {
        try {
            const redis = getRedis();
            const result = await redis.get(`execution:${executionId}`);
            return result;
        } catch (error) {
            logger.error('Get execution result error:', error);
            return null;
        }
    }

    async cancelExecution(executionId) {
        try {
            const execution = this.activeExecutions.get(executionId);
            if (!execution) {
                return false;
            }

            // Cancel in Docker sandbox
            await this.dockerSandbox.cancelExecution(executionId);
            
            // Remove from active executions
            this.activeExecutions.delete(executionId);

            logger.info('Execution cancelled', { executionId });
            return true;
        } catch (error) {
            logger.error('Cancel execution error:', error);
            return false;
        }
    }

    async getActiveExecutions() {
        return Array.from(this.activeExecutions.entries()).map(([id, execution]) => ({
            executionId: id,
            botId: execution.botId,
            startTime: execution.startTime,
            timeout: execution.timeout,
            duration: Date.now() - execution.startTime
        }));
    }

    async cleanupExpiredExecutions() {
        const now = Date.now();
        const expiredExecutions = [];

        for (const [executionId, execution] of this.activeExecutions.entries()) {
            if (now - execution.startTime > execution.timeout) {
                expiredExecutions.push(executionId);
            }
        }

        for (const executionId of expiredExecutions) {
            await this.cancelExecution(executionId);
            logger.warn('Execution timed out and was cancelled', { executionId });
        }

        return expiredExecutions.length;
    }

    async getResourceUsage() {
        try {
            const dockerStats = await this.dockerSandbox.getResourceUsage();
            const activeExecutions = this.activeExecutions.size;

            return {
                activeExecutions,
                maxConcurrentExecutions: this.maxConcurrentExecutions,
                docker: dockerStats
            };
        } catch (error) {
            logger.error('Get resource usage error:', error);
            return {
                activeExecutions: this.activeExecutions.size,
                maxConcurrentExecutions: this.maxConcurrentExecutions,
                docker: null
            };
        }
    }

    async shutdown() {
        try {
            logger.info('Shutting down bot runtime...');

            // Cancel all active executions
            const activeExecutionIds = Array.from(this.activeExecutions.keys());
            for (const executionId of activeExecutionIds) {
                await this.cancelExecution(executionId);
            }

            // Shutdown Docker sandbox
            await this.dockerSandbox.shutdown();

            logger.info('Bot runtime shutdown complete');
        } catch (error) {
            logger.error('Bot runtime shutdown error:', error);
        }
    }

    // Periodic cleanup task
    startCleanupTask() {
        this.cleanupInterval = setInterval(async () => {
            try {
                await this.cleanupExpiredExecutions();
            } catch (error) {
                logger.error('Cleanup task error:', error);
            }
        }, 30000); // Run every 30 seconds
    }

    stopCleanupTask() {
        if (this.cleanupInterval) {
            clearInterval(this.cleanupInterval);
            this.cleanupInterval = null;
        }
    }
}

module.exports = BotRuntime;
