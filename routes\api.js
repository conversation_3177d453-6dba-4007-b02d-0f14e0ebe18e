const express = require('express');
const Joi = require('joi');
const { getDatabase } = require('../config/database');
const { getRedis } = require('../config/redis');
const { authenticateBotToken, rateLimitBot } = require('../middleware/auth');
const BotRuntime = require('../services/BotRuntime');
const logger = require('../utils/logger');

const router = express.Router();

// Validation schemas
const executeCodeSchema = Joi.object({
    code: Joi.string().max(100000).required(),
    input: Joi.any(),
    timeout: Joi.number().min(1000).max(30000).default(10000)
});

const sendMessageSchema = Joi.object({
    content: Joi.string().max(10000).required(),
    metadata: Joi.object().default({})
});

// Bot API endpoints (require bot token authentication)
router.use('/bot', authenticateBotToken, rateLimitBot);

// Execute bot code
router.post('/bot/execute', async (req, res) => {
    try {
        const { error, value } = executeCodeSchema.validate(req.body);
        if (error) {
            return res.status(400).json({ error: error.details[0].message });
        }

        const { code, input, timeout } = value;
        const botRuntime = new BotRuntime();

        // Execute code in sandbox
        const result = await botRuntime.executeCode({
            botId: req.bot.id,
            code,
            input,
            timeout,
            language: 'javascript' // Default to JavaScript for now
        });

        // Log execution
        const db = getDatabase();
        await db.run(`
            INSERT INTO bot_executions (bot_id, execution_id, status, duration_ms, memory_used, cpu_used, error_message)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        `, [
            req.bot.id,
            result.executionId,
            result.status,
            result.duration,
            result.memoryUsed || null,
            result.cpuUsed || null,
            result.error || null
        ]);

        logger.logBotExecution(req.bot.id, result.executionId, result.status, {
            duration: result.duration,
            memoryUsed: result.memoryUsed
        });

        res.json({
            executionId: result.executionId,
            status: result.status,
            output: result.output,
            error: result.error,
            duration: result.duration,
            memoryUsed: result.memoryUsed,
            cpuUsed: result.cpuUsed
        });
    } catch (error) {
        logger.logBotError(req.bot.id, error, { endpoint: '/bot/execute' });
        res.status(500).json({ error: 'Code execution failed' });
    }
});

// Send message (for bot communication)
router.post('/bot/message', async (req, res) => {
    try {
        const { error, value } = sendMessageSchema.validate(req.body);
        if (error) {
            return res.status(400).json({ error: error.details[0].message });
        }

        const { content, metadata } = value;
        const db = getDatabase();

        // Store message
        const result = await db.run(`
            INSERT INTO messages (bot_id, token_id, message_type, content, metadata)
            VALUES (?, ?, ?, ?, ?)
        `, [
            req.bot.id,
            req.bot.tokenId,
            'outgoing',
            content,
            JSON.stringify(metadata)
        ]);

        // Publish message to Redis for real-time delivery
        const redis = getRedis();
        await redis.publish(`bot:${req.bot.id}:messages`, {
            id: result.id,
            botId: req.bot.id,
            type: 'outgoing',
            content,
            metadata,
            timestamp: new Date().toISOString()
        });

        res.json({
            messageId: result.id,
            status: 'sent',
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        logger.logBotError(req.bot.id, error, { endpoint: '/bot/message' });
        res.status(500).json({ error: 'Failed to send message' });
    }
});

// Get bot state
router.get('/bot/state', async (req, res) => {
    try {
        const redis = getRedis();
        const state = await redis.getBotState(req.bot.id);

        res.json({
            state: state || {},
            botId: req.bot.id
        });
    } catch (error) {
        logger.logBotError(req.bot.id, error, { endpoint: '/bot/state' });
        res.status(500).json({ error: 'Failed to get bot state' });
    }
});

// Update bot state
router.put('/bot/state', async (req, res) => {
    try {
        const stateSchema = Joi.object({
            state: Joi.object().required(),
            ttl: Joi.number().min(60).max(86400).default(3600) // 1 hour default
        });

        const { error, value } = stateSchema.validate(req.body);
        if (error) {
            return res.status(400).json({ error: error.details[0].message });
        }

        const { state, ttl } = value;
        const redis = getRedis();

        await redis.setBotState(req.bot.id, state, ttl);

        res.json({
            message: 'Bot state updated successfully',
            botId: req.bot.id,
            ttl
        });
    } catch (error) {
        logger.logBotError(req.bot.id, error, { endpoint: '/bot/state' });
        res.status(500).json({ error: 'Failed to update bot state' });
    }
});

// Get bot messages
router.get('/bot/messages', async (req, res) => {
    try {
        const db = getDatabase();
        const page = parseInt(req.query.page) || 1;
        const limit = Math.min(parseInt(req.query.limit) || 50, 100);
        const offset = (page - 1) * limit;
        const messageType = req.query.type; // 'incoming', 'outgoing', 'error'

        let query = `
            SELECT id, message_type, content, metadata, created_at
            FROM messages 
            WHERE bot_id = ?
        `;
        const params = [req.bot.id];

        if (messageType) {
            query += ' AND message_type = ?';
            params.push(messageType);
        }

        query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
        params.push(limit, offset);

        const messages = await db.all(query, params);

        // Parse metadata
        messages.forEach(message => {
            if (message.metadata) {
                try {
                    message.metadata = JSON.parse(message.metadata);
                } catch {
                    message.metadata = {};
                }
            }
        });

        res.json({
            messages,
            pagination: {
                page,
                limit,
                hasMore: messages.length === limit
            }
        });
    } catch (error) {
        logger.logBotError(req.bot.id, error, { endpoint: '/bot/messages' });
        res.status(500).json({ error: 'Failed to fetch messages' });
    }
});

// Public API endpoints (no authentication required)

// Health check for bots
router.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        services: {
            database: true, // Could add actual health checks
            redis: true,
            docker: true
        }
    });
});

// Get bot execution statistics (public, limited info)
router.get('/stats/executions', async (req, res) => {
    try {
        const db = getDatabase();
        const timeframe = req.query.timeframe || '24h'; // 1h, 24h, 7d, 30d
        
        let timeCondition = '';
        switch (timeframe) {
            case '1h':
                timeCondition = "AND start_time >= datetime('now', '-1 hour')";
                break;
            case '24h':
                timeCondition = "AND start_time >= datetime('now', '-1 day')";
                break;
            case '7d':
                timeCondition = "AND start_time >= datetime('now', '-7 days')";
                break;
            case '30d':
                timeCondition = "AND start_time >= datetime('now', '-30 days')";
                break;
        }

        const stats = await db.get(`
            SELECT 
                COUNT(*) as total_executions,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as successful_executions,
                COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_executions,
                COUNT(CASE WHEN status = 'timeout' THEN 1 END) as timeout_executions,
                AVG(duration_ms) as avg_duration_ms,
                MAX(duration_ms) as max_duration_ms,
                AVG(memory_used) as avg_memory_used
            FROM bot_executions 
            WHERE 1=1 ${timeCondition}
        `);

        res.json({
            timeframe,
            stats: {
                totalExecutions: stats.total_executions || 0,
                successfulExecutions: stats.successful_executions || 0,
                failedExecutions: stats.failed_executions || 0,
                timeoutExecutions: stats.timeout_executions || 0,
                avgDurationMs: Math.round(stats.avg_duration_ms || 0),
                maxDurationMs: stats.max_duration_ms || 0,
                avgMemoryUsed: Math.round(stats.avg_memory_used || 0),
                successRate: stats.total_executions > 0 
                    ? Math.round((stats.successful_executions / stats.total_executions) * 100) 
                    : 0
            }
        });
    } catch (error) {
        logger.error('Get execution stats error:', error);
        res.status(500).json({ error: 'Failed to fetch execution statistics' });
    }
});

module.exports = router;
