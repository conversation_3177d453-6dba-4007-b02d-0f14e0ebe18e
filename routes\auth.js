const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const Joi = require('joi');
const { getDatabase } = require('../config/database');
const { getRedis } = require('../config/redis');
const { authenticateToken } = require('../middleware/auth');
const logger = require('../utils/logger');

const router = express.Router();

// Validation schemas
const registerSchema = Joi.object({
    username: Joi.string().alphanum().min(3).max(50).required(),
    email: Joi.string().email().required(),
    password: Joi.string().min(6).max(100).required()
});

const loginSchema = Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().required()
});

const changePasswordSchema = Joi.object({
    currentPassword: Joi.string().required(),
    newPassword: Joi.string().min(6).max(100).required()
});

// Helper function to generate JWT token
const generateToken = (userId) => {
    return jwt.sign(
        { userId },
        process.env.JWT_SECRET,
        { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
    );
};

// Register new user
router.post('/register', async (req, res) => {
    try {
        // Validate input
        const { error, value } = registerSchema.validate(req.body);
        if (error) {
            return res.status(400).json({ error: error.details[0].message });
        }

        const { username, email, password } = value;
        const db = getDatabase();

        // Check if user already exists
        const existingUser = await db.get(
            'SELECT id FROM users WHERE email = ? OR username = ?',
            [email, username]
        );

        if (existingUser) {
            return res.status(409).json({ error: 'User already exists with this email or username' });
        }

        // Hash password
        const saltRounds = 12;
        const passwordHash = await bcrypt.hash(password, saltRounds);

        // Create user
        const result = await db.run(
            'INSERT INTO users (username, email, password_hash) VALUES (?, ?, ?)',
            [username, email, passwordHash]
        );

        const userId = result.id;

        // Generate token
        const token = generateToken(userId);

        // Get created user (without password)
        const user = await db.get(
            'SELECT id, username, email, role, created_at FROM users WHERE id = ?',
            [userId]
        );

        logger.info('User registered successfully', { userId, username, email });

        res.status(201).json({
            message: 'User registered successfully',
            user,
            token
        });
    } catch (error) {
        logger.error('Registration error:', error);
        res.status(500).json({ error: 'Registration failed' });
    }
});

// Login user
router.post('/login', async (req, res) => {
    try {
        // Validate input
        const { error, value } = loginSchema.validate(req.body);
        if (error) {
            return res.status(400).json({ error: error.details[0].message });
        }

        const { email, password } = value;
        const db = getDatabase();

        // Get user with password
        const user = await db.get(
            'SELECT id, username, email, password_hash, role, is_active FROM users WHERE email = ?',
            [email]
        );

        if (!user) {
            return res.status(401).json({ error: 'Invalid credentials' });
        }

        if (!user.is_active) {
            return res.status(401).json({ error: 'Account is deactivated' });
        }

        // Verify password
        const isValidPassword = await bcrypt.compare(password, user.password_hash);
        if (!isValidPassword) {
            logger.logSecurityEvent('failed_login_attempt', { email });
            return res.status(401).json({ error: 'Invalid credentials' });
        }

        // Generate token
        const token = generateToken(user.id);

        // Remove password from response
        const { password_hash, ...userWithoutPassword } = user;

        logger.info('User logged in successfully', { userId: user.id, username: user.username });

        res.json({
            message: 'Login successful',
            user: userWithoutPassword,
            token
        });
    } catch (error) {
        logger.error('Login error:', error);
        res.status(500).json({ error: 'Login failed' });
    }
});

// Logout user (blacklist token)
router.post('/logout', authenticateToken, async (req, res) => {
    try {
        const redis = getRedis();
        const token = req.token;

        // Add token to blacklist
        const decoded = jwt.decode(token);
        const expiresIn = decoded.exp - Math.floor(Date.now() / 1000);
        
        if (expiresIn > 0) {
            await redis.set(`blacklist:${token}`, true, expiresIn);
        }

        logger.info('User logged out successfully', { userId: req.user.id });

        res.json({ message: 'Logout successful' });
    } catch (error) {
        logger.error('Logout error:', error);
        res.status(500).json({ error: 'Logout failed' });
    }
});

// Get current user profile
router.get('/profile', authenticateToken, async (req, res) => {
    try {
        const db = getDatabase();
        const user = await db.get(
            'SELECT id, username, email, role, is_active, created_at, updated_at FROM users WHERE id = ?',
            [req.user.id]
        );

        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }

        res.json({ user });
    } catch (error) {
        logger.error('Profile fetch error:', error);
        res.status(500).json({ error: 'Failed to fetch profile' });
    }
});

// Update user profile
router.put('/profile', authenticateToken, async (req, res) => {
    try {
        const updateSchema = Joi.object({
            username: Joi.string().alphanum().min(3).max(50),
            email: Joi.string().email()
        });

        const { error, value } = updateSchema.validate(req.body);
        if (error) {
            return res.status(400).json({ error: error.details[0].message });
        }

        const db = getDatabase();
        const updates = [];
        const params = [];

        if (value.username) {
            // Check if username is already taken
            const existingUser = await db.get(
                'SELECT id FROM users WHERE username = ? AND id != ?',
                [value.username, req.user.id]
            );
            if (existingUser) {
                return res.status(409).json({ error: 'Username already taken' });
            }
            updates.push('username = ?');
            params.push(value.username);
        }

        if (value.email) {
            // Check if email is already taken
            const existingUser = await db.get(
                'SELECT id FROM users WHERE email = ? AND id != ?',
                [value.email, req.user.id]
            );
            if (existingUser) {
                return res.status(409).json({ error: 'Email already taken' });
            }
            updates.push('email = ?');
            params.push(value.email);
        }

        if (updates.length === 0) {
            return res.status(400).json({ error: 'No valid fields to update' });
        }

        updates.push('updated_at = CURRENT_TIMESTAMP');
        params.push(req.user.id);

        await db.run(
            `UPDATE users SET ${updates.join(', ')} WHERE id = ?`,
            params
        );

        // Get updated user
        const updatedUser = await db.get(
            'SELECT id, username, email, role, is_active, created_at, updated_at FROM users WHERE id = ?',
            [req.user.id]
        );

        logger.info('User profile updated', { userId: req.user.id, updates: Object.keys(value) });

        res.json({
            message: 'Profile updated successfully',
            user: updatedUser
        });
    } catch (error) {
        logger.error('Profile update error:', error);
        res.status(500).json({ error: 'Failed to update profile' });
    }
});

// Change password
router.put('/password', authenticateToken, async (req, res) => {
    try {
        const { error, value } = changePasswordSchema.validate(req.body);
        if (error) {
            return res.status(400).json({ error: error.details[0].message });
        }

        const { currentPassword, newPassword } = value;
        const db = getDatabase();

        // Get current user with password
        const user = await db.get(
            'SELECT password_hash FROM users WHERE id = ?',
            [req.user.id]
        );

        // Verify current password
        const isValidPassword = await bcrypt.compare(currentPassword, user.password_hash);
        if (!isValidPassword) {
            return res.status(401).json({ error: 'Current password is incorrect' });
        }

        // Hash new password
        const saltRounds = 12;
        const newPasswordHash = await bcrypt.hash(newPassword, saltRounds);

        // Update password
        await db.run(
            'UPDATE users SET password_hash = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [newPasswordHash, req.user.id]
        );

        logger.info('User password changed', { userId: req.user.id });

        res.json({ message: 'Password changed successfully' });
    } catch (error) {
        logger.error('Password change error:', error);
        res.status(500).json({ error: 'Failed to change password' });
    }
});

// Verify token (for client-side token validation)
router.get('/verify', authenticateToken, (req, res) => {
    res.json({
        valid: true,
        user: req.user
    });
});

module.exports = router;
