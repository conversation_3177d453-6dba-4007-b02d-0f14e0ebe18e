version: '3.8'

services:
  # Main application
  bot-platform:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - DB_PATH=/app/data/bot_platform.db
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - JWT_SECRET=${JWT_SECRET:-change-this-secret-in-production}
      - DOCKER_SOCKET=/var/run/docker.sock
      - BOT_SANDBOX_IMAGE=bot-sandbox:latest
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - /var/run/docker.sock:/var/run/docker.sock
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - bot-platform-network

  # Redis for caching and pub/sub
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped
    networks:
      - bot-platform-network

  # Redis Commander (optional, for Redis management)
  redis-commander:
    image: rediscommander/redis-commander:latest
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - bot-platform-network
    profiles:
      - tools

  # Nginx reverse proxy (optional)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - bot-platform
    restart: unless-stopped
    networks:
      - bot-platform-network
    profiles:
      - production

volumes:
  redis-data:
    driver: local

networks:
  bot-platform-network:
    driver: bridge
