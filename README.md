# Bot Creation Platform

A comprehensive bot creation platform with sandboxed execution, real-time communication, and secure API management.

## Features

### 🏗️ Layered Architecture
- **Backend API Server**: Authentication, bot management, and API endpoints
- **Bot Runtime Environment**: Sandboxed code execution with Docker containers
- **Real-time Communication**: WebSocket/Socket.IO for live bot interactions
- **Database Layer**: SQLite with Redis caching for optimal performance

### 🔒 Security Features
- JWT-based authentication and authorization
- Docker sandbox isolation for bot code execution
- Input/output validation and sanitization
- Rate limiting and resource management
- Code pattern analysis to prevent malicious code

### 🚀 Performance & Scalability
- Resource limiting (CPU, memory, network)
- Concurrent execution management
- Redis caching for session and state management
- Efficient database indexing and queries

### 🛠️ Developer Experience
- RESTful API design
- Real-time WebSocket events
- Comprehensive logging and monitoring
- Docker containerization for easy deployment

## Tech Stack

- **Backend**: Node.js, Express.js
- **Database**: SQLite with Redis caching
- **Containerization**: Docker, Docker Compose
- **Real-time**: Socket.IO
- **Authentication**: JWT tokens
- **Languages Supported**: JavaScript, Python

## Quick Start

### Prerequisites

- Node.js 18+ 
- Docker and Docker Compose
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd bot_create
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Initialize the database**
   ```bash
   npm run migrate
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

### Using Docker

1. **Build and start all services**
   ```bash
   docker-compose up -d
   ```

2. **View logs**
   ```bash
   docker-compose logs -f bot-platform
   ```

3. **Stop services**
   ```bash
   docker-compose down
   ```

## API Documentation

### Authentication Endpoints

#### Register User
```http
POST /api/auth/register
Content-Type: application/json

{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "securepassword"
}
```

#### Login
```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securepassword"
}
```

### Bot Management Endpoints

#### Create Bot
```http
POST /api/bots
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "name": "My Bot",
  "description": "A simple bot",
  "language": "javascript"
}
```

#### Update Bot Code
```http
PUT /api/bots/:id
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "code": "console.log('Hello, World!');"
}
```

#### Create Bot Token
```http
POST /api/bots/:id/tokens
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "name": "API Token",
  "permissions": {
    "execute": true,
    "message": true
  }
}
```

### Bot API Endpoints

#### Execute Code
```http
POST /api/bot/execute
Authorization: Bearer <bot_token>
Content-Type: application/json

{
  "code": "console.log('Hello from bot!');",
  "input": {"key": "value"},
  "timeout": 10000
}
```

#### Send Message
```http
POST /api/bot/message
Authorization: Bearer <bot_token>
Content-Type: application/json

{
  "content": "Hello, I'm a bot!",
  "metadata": {"type": "greeting"}
}
```

## WebSocket Events

### Client Events

#### Authenticate
```javascript
socket.emit('authenticate', { token: 'jwt_token' });
```

#### Join Bot Room
```javascript
socket.emit('join_bot_room', { botId: 123 });
```

#### Execute Bot Code
```javascript
socket.emit('execute_bot_code', {
  botId: 123,
  code: 'console.log("Hello");',
  input: {},
  timeout: 10000
});
```

### Server Events

#### Authentication Success
```javascript
socket.on('authenticated', (data) => {
  console.log('User authenticated:', data.user);
});
```

#### Bot Execution Result
```javascript
socket.on('bot_execution_result', (data) => {
  console.log('Execution result:', data);
});
```

#### Bot Message
```javascript
socket.on('bot_message', (data) => {
  console.log('Bot message:', data);
});
```

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `PORT` | Server port | `3000` |
| `NODE_ENV` | Environment | `development` |
| `DB_PATH` | SQLite database path | `./database/bot_platform.db` |
| `REDIS_HOST` | Redis host | `localhost` |
| `REDIS_PORT` | Redis port | `6379` |
| `JWT_SECRET` | JWT signing secret | Required |
| `BOT_EXECUTION_TIMEOUT` | Default execution timeout (ms) | `30000` |
| `MAX_BOT_MEMORY` | Maximum bot memory | `128m` |
| `MAX_BOT_CPU` | Maximum bot CPU usage | `0.5` |

### Docker Configuration

The platform uses Docker for sandboxed bot execution. The sandbox image is automatically built with:

- Node.js 18 runtime
- Python 3 support
- Security restrictions
- Resource limitations

## Security Considerations

### Code Execution Security

- **Sandboxed Environment**: All bot code runs in isolated Docker containers
- **Resource Limits**: CPU, memory, and execution time constraints
- **Network Isolation**: Optional network isolation for bot containers
- **Code Validation**: Pattern analysis to detect potentially dangerous code
- **Input Sanitization**: All inputs are validated and sanitized

### API Security

- **JWT Authentication**: Secure token-based authentication
- **Rate Limiting**: Configurable rate limits per user/bot
- **CORS Protection**: Cross-origin request protection
- **Helmet.js**: Security headers and protection
- **Input Validation**: Joi schema validation for all inputs

## Monitoring and Logging

### Logging

The platform uses Winston for structured logging:

- **Console Output**: Development environment
- **File Logging**: Production environment
- **Log Levels**: Error, warn, info, debug
- **Structured Data**: JSON format with metadata

### Monitoring

- **Health Checks**: `/health` endpoint for service monitoring
- **Execution Statistics**: `/api/stats/executions` for performance metrics
- **Resource Usage**: Docker container monitoring
- **Real-time Events**: WebSocket connection monitoring

## Development

### Project Structure

```
bot_create/
├── config/           # Configuration files
├── middleware/       # Express middleware
├── routes/          # API route handlers
├── services/        # Business logic services
├── utils/           # Utility functions
├── database/        # Database migrations and schema
├── sandbox/         # Docker sandbox configuration
├── logs/            # Application logs
└── data/            # SQLite database files
```

### Running Tests

```bash
npm test
```

### Code Style

The project follows standard JavaScript conventions with:

- ESLint for code linting
- Prettier for code formatting
- JSDoc for documentation

## Deployment

### Production Deployment

1. **Set environment variables**
   ```bash
   export NODE_ENV=production
   export JWT_SECRET=your-secure-secret
   # Set other production variables
   ```

2. **Build and deploy with Docker**
   ```bash
   docker-compose -f docker-compose.yml --profile production up -d
   ```

3. **Set up reverse proxy (optional)**
   - Configure Nginx for SSL termination
   - Set up domain and certificates

### Scaling Considerations

- **Horizontal Scaling**: Multiple application instances behind load balancer
- **Database**: Consider PostgreSQL for larger deployments
- **Redis Cluster**: For high availability caching
- **Container Orchestration**: Kubernetes for large-scale deployments

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:

- Create an issue on GitHub
- Check the documentation
- Review the API examples

## Roadmap

- [ ] Python bot execution support
- [ ] Web-based code editor
- [ ] Bot marketplace
- [ ] Advanced monitoring dashboard
- [ ] Webhook integrations
- [ ] Multi-tenant support
- [ ] Android app development
