const axios = require('axios');
const io = require('socket.io-client');

/**
 * Bot Client SDK - Similar to Discord.js
 * Allows developers to create bots that connect to the Bot Platform
 */
class BotClient {
    constructor(token, options = {}) {
        this.token = token;
        this.baseURL = options.baseURL || 'http://localhost:3000';
        this.socket = null;
        this.connected = false;
        this.bot = null;
        this.eventHandlers = new Map();
        
        // Default options
        this.options = {
            autoReconnect: true,
            reconnectDelay: 5000,
            ...options
        };
    }

    /**
     * Connect bot to the platform
     */
    async login() {
        try {
            console.log('🤖 Connecting bot to platform...');
            
            // Verify token and get bot info
            await this.verifyToken();
            
            // Connect WebSocket
            await this.connectWebSocket();
            
            console.log(`✅ Bot "${this.bot.name}" connected successfully!`);
            this.connected = true;
            
            this.emit('ready', this.bot);
            
        } catch (error) {
            console.error('❌ Failed to connect bot:', error.message);
            throw error;
        }
    }

    /**
     * Verify bot token and get bot information
     */
    async verifyToken() {
        try {
            const response = await this.makeRequest('GET', '/bot/state');
            this.bot = {
                id: response.botId,
                name: 'Bot', // We'll get this from another endpoint
                token: this.token
            };
        } catch (error) {
            throw new Error('Invalid bot token');
        }
    }

    /**
     * Connect WebSocket for real-time events
     */
    async connectWebSocket() {
        return new Promise((resolve, reject) => {
            this.socket = io(this.baseURL);
            
            this.socket.on('connect', () => {
                console.log('🔌 WebSocket connected');
                
                // Authenticate bot
                this.socket.emit('authenticate_bot', { token: this.token });
            });

            this.socket.on('bot_authenticated', (data) => {
                console.log('🔐 Bot authenticated via WebSocket');
                this.bot = { ...this.bot, ...data.bot };
                resolve();
            });

            this.socket.on('bot_auth_error', (error) => {
                console.error('🚫 Bot authentication failed:', error);
                reject(new Error(error.error));
            });

            this.socket.on('message', (message) => {
                this.emit('message', new Message(message, this));
            });

            this.socket.on('disconnect', () => {
                console.log('🔌 WebSocket disconnected');
                this.connected = false;
                this.emit('disconnect');
                
                if (this.options.autoReconnect) {
                    setTimeout(() => {
                        console.log('🔄 Attempting to reconnect...');
                        this.connectWebSocket();
                    }, this.options.reconnectDelay);
                }
            });

            this.socket.on('error', (error) => {
                console.error('🚫 WebSocket error:', error);
                this.emit('error', error);
            });
        });
    }

    /**
     * Send a message to the platform
     */
    async sendMessage(content, metadata = {}) {
        try {
            const response = await this.makeRequest('POST', '/bot/message', {
                content,
                metadata
            });
            
            console.log(`📤 Message sent: "${content}"`);
            return response;
            
        } catch (error) {
            console.error('❌ Failed to send message:', error.message);
            throw error;
        }
    }

    /**
     * Execute code (for advanced bots)
     */
    async executeCode(code, input = null, timeout = 10000) {
        try {
            const response = await this.makeRequest('POST', '/bot/execute', {
                code,
                input,
                timeout
            });
            
            console.log(`⚡ Code executed: ${response.status}`);
            return response;
            
        } catch (error) {
            console.error('❌ Code execution failed:', error.message);
            throw error;
        }
    }

    /**
     * Get bot state
     */
    async getState() {
        try {
            const response = await this.makeRequest('GET', '/bot/state');
            return response.state;
        } catch (error) {
            console.error('❌ Failed to get state:', error.message);
            return {};
        }
    }

    /**
     * Update bot state
     */
    async setState(state, ttl = 3600) {
        try {
            const response = await this.makeRequest('PUT', '/bot/state', {
                state,
                ttl
            });
            
            console.log('💾 Bot state updated');
            return response;
            
        } catch (error) {
            console.error('❌ Failed to update state:', error.message);
            throw error;
        }
    }

    /**
     * Register event handler
     */
    on(event, handler) {
        if (!this.eventHandlers.has(event)) {
            this.eventHandlers.set(event, []);
        }
        this.eventHandlers.get(event).push(handler);
    }

    /**
     * Emit event to handlers
     */
    emit(event, ...args) {
        const handlers = this.eventHandlers.get(event);
        if (handlers) {
            handlers.forEach(handler => {
                try {
                    handler(...args);
                } catch (error) {
                    console.error(`Error in ${event} handler:`, error);
                }
            });
        }
    }

    /**
     * Make HTTP request to bot API
     */
    async makeRequest(method, endpoint, data = null) {
        try {
            const config = {
                method,
                url: `${this.baseURL}/api${endpoint}`,
                headers: {
                    'Authorization': `Bearer ${this.token}`,
                    'Content-Type': 'application/json'
                }
            };

            if (data) {
                config.data = data;
            }

            const response = await axios(config);
            return response.data;
            
        } catch (error) {
            if (error.response) {
                throw new Error(error.response.data.error || 'API request failed');
            } else {
                throw new Error('Network error');
            }
        }
    }

    /**
     * Disconnect bot
     */
    disconnect() {
        if (this.socket) {
            this.socket.disconnect();
        }
        this.connected = false;
        console.log('👋 Bot disconnected');
    }

    /**
     * Get bot information
     */
    getBot() {
        return this.bot;
    }

    /**
     * Check if bot is connected
     */
    isConnected() {
        return this.connected;
    }
}

/**
 * Message class - represents a message from the platform
 */
class Message {
    constructor(data, client) {
        this.id = data.id;
        this.content = data.content;
        this.type = data.type;
        this.metadata = data.metadata || {};
        this.timestamp = new Date(data.timestamp);
        this.client = client;
    }

    /**
     * Reply to this message
     */
    async reply(content, metadata = {}) {
        return await this.client.sendMessage(content, {
            ...metadata,
            replyTo: this.id
        });
    }

    /**
     * Check if message is from user
     */
    isFromUser() {
        return this.type === 'incoming';
    }

    /**
     * Get message age in milliseconds
     */
    getAge() {
        return Date.now() - this.timestamp.getTime();
    }
}

module.exports = { BotClient, Message };
