const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs').promises;
const logger = require('../utils/logger');

class Database {
    constructor() {
        this.db = null;
        this.dbPath = process.env.DB_PATH || './database/bot_platform.db';
    }

    async initialize() {
        try {
            // Ensure database directory exists
            const dbDir = path.dirname(this.dbPath);
            await fs.mkdir(dbDir, { recursive: true });

            // Connect to database
            this.db = new sqlite3.Database(this.dbPath, (err) => {
                if (err) {
                    logger.error('Error opening database:', err);
                    throw err;
                }
                logger.info(`Connected to SQLite database at ${this.dbPath}`);
            });

            // Enable foreign keys
            await this.run('PRAGMA foreign_keys = ON');
            
            // Create tables
            await this.createTables();
            
            return this.db;
        } catch (error) {
            logger.error('Database initialization failed:', error);
            throw error;
        }
    }

    async createTables() {
        const tables = [
            // Users table
            `CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                role VARCHAR(20) DEFAULT 'user',
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Bots table
            `CREATE TABLE IF NOT EXISTS bots (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                language VARCHAR(20) DEFAULT 'javascript',
                code TEXT,
                is_active BOOLEAN DEFAULT 1,
                is_deployed BOOLEAN DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
            )`,

            // Bot tokens table
            `CREATE TABLE IF NOT EXISTS bot_tokens (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                bot_id INTEGER NOT NULL,
                token VARCHAR(255) UNIQUE NOT NULL,
                name VARCHAR(100),
                permissions TEXT, -- JSON string
                is_active BOOLEAN DEFAULT 1,
                expires_at DATETIME,
                last_used_at DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (bot_id) REFERENCES bots (id) ON DELETE CASCADE
            )`,

            // Messages table (for logging bot interactions)
            `CREATE TABLE IF NOT EXISTS messages (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                bot_id INTEGER NOT NULL,
                token_id INTEGER,
                message_type VARCHAR(50), -- 'incoming', 'outgoing', 'error'
                content TEXT,
                metadata TEXT, -- JSON string
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (bot_id) REFERENCES bots (id) ON DELETE CASCADE,
                FOREIGN KEY (token_id) REFERENCES bot_tokens (id) ON DELETE SET NULL
            )`,

            // Bot executions table (for monitoring)
            `CREATE TABLE IF NOT EXISTS bot_executions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                bot_id INTEGER NOT NULL,
                execution_id VARCHAR(255) UNIQUE NOT NULL,
                status VARCHAR(50), -- 'running', 'completed', 'failed', 'timeout'
                start_time DATETIME DEFAULT CURRENT_TIMESTAMP,
                end_time DATETIME,
                duration_ms INTEGER,
                memory_used INTEGER,
                cpu_used REAL,
                error_message TEXT,
                FOREIGN KEY (bot_id) REFERENCES bots (id) ON DELETE CASCADE
            )`,

            // API keys table (for external integrations)
            `CREATE TABLE IF NOT EXISTS api_keys (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                key_name VARCHAR(100) NOT NULL,
                api_key VARCHAR(255) UNIQUE NOT NULL,
                permissions TEXT, -- JSON string
                is_active BOOLEAN DEFAULT 1,
                last_used_at DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
            )`
        ];

        for (const table of tables) {
            await this.run(table);
        }

        // Create indexes for better performance
        const indexes = [
            'CREATE INDEX IF NOT EXISTS idx_bots_user_id ON bots (user_id)',
            'CREATE INDEX IF NOT EXISTS idx_bot_tokens_bot_id ON bot_tokens (bot_id)',
            'CREATE INDEX IF NOT EXISTS idx_bot_tokens_token ON bot_tokens (token)',
            'CREATE INDEX IF NOT EXISTS idx_messages_bot_id ON messages (bot_id)',
            'CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages (created_at)',
            'CREATE INDEX IF NOT EXISTS idx_bot_executions_bot_id ON bot_executions (bot_id)',
            'CREATE INDEX IF NOT EXISTS idx_bot_executions_status ON bot_executions (status)',
            'CREATE INDEX IF NOT EXISTS idx_api_keys_user_id ON api_keys (user_id)',
            'CREATE INDEX IF NOT EXISTS idx_api_keys_key ON api_keys (api_key)'
        ];

        for (const index of indexes) {
            await this.run(index);
        }

        logger.info('Database tables and indexes created successfully');
    }

    // Promisify database operations
    run(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.run(sql, params, function(err) {
                if (err) {
                    logger.error('Database run error:', err);
                    reject(err);
                } else {
                    resolve({ id: this.lastID, changes: this.changes });
                }
            });
        });
    }

    get(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.get(sql, params, (err, row) => {
                if (err) {
                    logger.error('Database get error:', err);
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }

    all(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    logger.error('Database all error:', err);
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    async close() {
        return new Promise((resolve, reject) => {
            if (this.db) {
                this.db.close((err) => {
                    if (err) {
                        logger.error('Error closing database:', err);
                        reject(err);
                    } else {
                        logger.info('Database connection closed');
                        resolve();
                    }
                });
            } else {
                resolve();
            }
        });
    }
}

// Singleton instance
let dbInstance = null;

async function initializeDatabase() {
    if (!dbInstance) {
        dbInstance = new Database();
        await dbInstance.initialize();
    }
    return dbInstance;
}

function getDatabase() {
    if (!dbInstance) {
        throw new Error('Database not initialized. Call initializeDatabase() first.');
    }
    return dbInstance;
}

module.exports = {
    initializeDatabase,
    getDatabase,
    Database
};
