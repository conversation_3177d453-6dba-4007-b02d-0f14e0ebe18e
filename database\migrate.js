#!/usr/bin/env node

require('dotenv').config();
const { initializeDatabase } = require('../config/database');
const logger = require('../utils/logger');

async function runMigrations() {
    try {
        logger.info('Starting database migrations...');
        
        // Initialize database (this will create tables if they don't exist)
        const db = await initializeDatabase();
        
        logger.info('Database migrations completed successfully');
        
        // Close database connection
        await db.close();
        
        process.exit(0);
    } catch (error) {
        logger.error('Database migration failed:', error);
        process.exit(1);
    }
}

// Run migrations if this file is executed directly
if (require.main === module) {
    runMigrations();
}

module.exports = { runMigrations };
