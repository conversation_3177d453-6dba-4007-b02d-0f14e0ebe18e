# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
DB_PATH=./database/bot_platform.db

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h

# Docker Configuration
DOCKER_SOCKET=/var/run/docker.sock
BOT_SANDBOX_IMAGE=bot-sandbox:latest
BOT_EXECUTION_TIMEOUT=30000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Bot Runtime Configuration
MAX_BOT_MEMORY=128m
MAX_BOT_CPU=0.5
BOT_NETWORK_ISOLATION=true

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log
