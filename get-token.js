#!/usr/bin/env node

const axios = require('axios');

const BASE_URL = 'http://localhost:3000';

async function getToken(email = '<EMAIL>', password = 'testpassword123') {
    try {
        console.log('🔑 Getting authentication token...');
        
        const response = await axios.post(`${BASE_URL}/api/auth/login`, {
            email,
            password
        });

        const { token, user } = response.data;
        
        console.log('✅ Login successful!');
        console.log('\n📋 User Info:');
        console.log(`   ID: ${user.id}`);
        console.log(`   Username: ${user.username}`);
        console.log(`   Email: ${user.email}`);
        console.log(`   Role: ${user.role}`);
        
        console.log('\n🎫 JWT Token:');
        console.log(`   ${token}`);
        
        console.log('\n📝 Usage Examples:');
        console.log('   # Test authenticated endpoint:');
        console.log(`   curl -H "Authorization: Bearer ${token}" http://localhost:3000/api/auth/profile`);
        
        console.log('\n   # Save token to environment variable:');
        console.log(`   export AUTH_TOKEN="${token}"`);
        
        return token;
        
    } catch (error) {
        if (error.response?.status === 401) {
            console.log('❌ Invalid credentials');
            console.log('💡 Try creating a new user first:');
            console.log(`   curl -X POST ${BASE_URL}/api/auth/register -H "Content-Type: application/json" -d '{"username":"newuser","email":"<EMAIL>","password":"newpassword123"}'`);
        } else {
            console.error('❌ Error:', error.response?.data || error.message);
        }
        process.exit(1);
    }
}

// Command line usage
if (require.main === module) {
    const email = process.argv[2] || '<EMAIL>';
    const password = process.argv[3] || 'testpassword123';
    
    console.log(`📧 Email: ${email}`);
    console.log(`🔒 Password: ${password}`);
    console.log('');
    
    getToken(email, password);
}

module.exports = getToken;
