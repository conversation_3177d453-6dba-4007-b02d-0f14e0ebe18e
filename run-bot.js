#!/usr/bin/env node

const { BotClient } = require('./bot-sdk/BotClient');

/**
 * Bot Runner - Quick way to test bot functionality
 */
class BotRunner {
    constructor() {
        this.token = null;
        this.client = null;
    }

    async run() {
        console.log('🤖 Bot Platform - Bot Runner');
        console.log('============================\n');

        // Get token from command line or environment
        this.token = process.argv[2] || process.env.BOT_TOKEN;

        if (!this.token) {
            this.showHelp();
            return;
        }

        console.log(`🔑 Using token: ${this.token.substring(0, 20)}...`);
        console.log('🚀 Starting bot...\n');

        try {
            this.client = new BotClient(this.token);
            
            // Setup event handlers
            this.setupEventHandlers();
            
            // Connect bot
            await this.client.login();
            
            // Keep process alive
            this.keepAlive();
            
        } catch (error) {
            console.error('❌ Failed to start bot:', error.message);
            
            if (error.message.includes('Invalid bot token')) {
                console.log('\n💡 How to get a bot token:');
                console.log('1. Go to http://localhost:3000');
                console.log('2. Login to your account');
                console.log('3. Create a new bot');
                console.log('4. Generate a bot token');
                console.log('5. Copy the token and use it here');
            }
            
            process.exit(1);
        }
    }

    setupEventHandlers() {
        // Bot ready
        this.client.on('ready', (bot) => {
            console.log(`✅ Bot "${bot.name}" connected successfully!`);
            console.log(`📋 Bot ID: ${bot.id}`);
            console.log('💬 Bot is now listening for messages...\n');
            
            // Send welcome message
            this.client.sendMessage('🤖 Hello! I\'m a test bot. Send me messages and I\'ll echo them back! Type !help for available commands.');
        });

        // Message received
        this.client.on('message', async (message) => {
            console.log(`📨 [${message.type}] ${message.content}`);

            // Only respond to user messages
            if (message.isFromUser()) {
                await this.handleUserMessage(message);
            }
        });

        // Error handling
        this.client.on('error', (error) => {
            console.error('🚫 Bot error:', error);
        });

        // Disconnect
        this.client.on('disconnect', () => {
            console.log('🔌 Bot disconnected');
        });
    }

    async handleUserMessage(message) {
        const content = message.content.toLowerCase().trim();

        try {
            // Command handling
            if (content.startsWith('!')) {
                await this.handleCommand(message, content);
            } else {
                // Echo back regular messages
                const response = `🔄 Echo: ${message.content}`;
                await message.reply(response);
                console.log(`📤 Replied: ${response}`);
            }
        } catch (error) {
            console.error('Error handling message:', error);
            await message.reply('❌ Sorry, I encountered an error processing your message.');
        }
    }

    async handleCommand(message, content) {
        const [command, ...args] = content.slice(1).split(' ');

        switch (command) {
            case 'help':
                await this.showHelpCommand(message);
                break;

            case 'ping':
                await message.reply('🏓 Pong!');
                break;

            case 'time':
                await message.reply(`🕐 Current time: ${new Date().toLocaleString()}`);
                break;

            case 'echo':
                const text = args.join(' ');
                await message.reply(`🔊 Echo: ${text || 'Nothing to echo!'}`);
                break;

            default:
                await message.reply(`❓ Unknown command: ${command}. Type !help for available commands.`);
        }
    }

    async showHelpCommand(message) {
        const helpText = `
🤖 **Bot Commands Help**

**Available Commands:**
• \`!help\` - Show this help message
• \`!ping\` - Test bot responsiveness
• \`!time\` - Get current time
• \`!echo <text>\` - Echo your message

**Regular Chat:**
Just type normally and I'll echo your message back!

Type any command to get started! 🚀
        `;

        await message.reply(helpText);
    }

    keepAlive() {
        console.log('🔄 Bot is running. Press Ctrl+C to stop.\n');
        
        // Handle graceful shutdown
        process.on('SIGINT', () => {
            console.log('\n👋 Shutting down bot...');
            if (this.client) {
                this.client.disconnect();
            }
            process.exit(0);
        });

        process.on('SIGTERM', () => {
            console.log('\n👋 Shutting down bot...');
            if (this.client) {
                this.client.disconnect();
            }
            process.exit(0);
        });
    }

    showHelp() {
        console.log('❌ Bot token required!\n');
        console.log('Usage:');
        console.log('  node run-bot.js <BOT_TOKEN>');
        console.log('  BOT_TOKEN=your_token node run-bot.js\n');
        
        console.log('💡 How to get a bot token:');
        console.log('1. Start the platform server: node simple-server.js');
        console.log('2. Go to http://localhost:3000');
        console.log('3. Login or register an account');
        console.log('4. Create a new bot');
        console.log('5. Generate a bot token');
        console.log('6. Copy the token and use it here\n');
        
        console.log('Example:');
        console.log('  node run-bot.js bot_abc123def456...\n');
        
        console.log('For a more advanced bot example:');
        console.log('  node examples/simple-bot.js\n');
    }
}

// Run if this file is executed directly
if (require.main === module) {
    const runner = new BotRunner();
    runner.run();
}

module.exports = BotRunner;
